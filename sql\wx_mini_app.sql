/*
 Navicat Premium Data Transfer

 Source Server         : capybara
 Source Server Type    : MySQL
 Source Server Version : 80036 (8.0.36)
 Source Host           : rm-bp1smj298drv75ayswo.mysql.rds.aliyuncs.com:3306
 Source Schema         : wx_mini_app

 Target Server Type    : MySQL
 Target Server Version : 80036 (8.0.36)
 File Encoding         : 65001

 Date: 18/02/2025 20:30:31
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for gen_table
-- ----------------------------
DROP TABLE IF EXISTS `gen_table`;
CREATE TABLE `gen_table`  (
  `table_id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '表名称',
  `table_comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '表描述',
  `sub_table_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联子表的表名',
  `sub_table_fk_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '子表关联的外键名',
  `class_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '实体类名称',
  `tpl_category` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'crud' COMMENT '使用的模板（crud单表操作 tree树表操作）',
  `tpl_web_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '前端模板类型（element-ui模版 element-plus模版）',
  `package_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生成包路径',
  `module_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生成模块名',
  `business_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生成业务名',
  `function_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生成功能名',
  `function_author` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生成功能作者',
  `gen_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '生成代码方式（0zip压缩包 1自定义路径）',
  `gen_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '/' COMMENT '生成路径（不填默认项目路径）',
  `options` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其它生成选项',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`table_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代码生成业务表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of gen_table
-- ----------------------------
INSERT INTO `gen_table` VALUES (1, 'sys_notice', '通知公告表', NULL, NULL, 'SysNotice', 'crud', '', 'com.fs.system', 'system', 'notice', '通知公告', 'zy', '0', '/', NULL, 'admin', '2024-12-20 15:40:49', '', NULL, NULL);
INSERT INTO `gen_table` VALUES (2, 'user_info', '用户信息', NULL, NULL, 'UserInfo', 'crud', 'element-ui', 'com.fs.operation', 'operation', 'userInfo', '用户信息', 'zy', '0', '/', '{\"parentMenuId\":2000}', 'admin', '2024-12-22 10:25:59', '', '2025-02-07 17:17:26', NULL);
INSERT INTO `gen_table` VALUES (3, 'cases', '案件表', NULL, NULL, 'Cases', 'crud', 'element-ui', 'com.fs.swap.operation', 'operation', 'cases', '案件列表', 'zy', '0', '/', '{\"parentMenuId\":2000}', 'admin', '2024-12-30 14:16:22', '', '2025-02-10 19:45:47', NULL);
INSERT INTO `gen_table` VALUES (4, 'review_daily_count', '每日免费评审次数', NULL, NULL, 'ReviewDailyCount', 'crud', 'element-ui', 'com.fs.swap.system', 'system', 'count', '每日免费评审次数记录', 'zy', '0', '/', '{}', 'admin', '2025-01-03 13:54:33', '', '2025-01-03 13:56:31', NULL);
INSERT INTO `gen_table` VALUES (5, 'review_paid_package', '付费评审次数', NULL, NULL, 'ReviewPaidPackage', 'crud', 'element-ui', 'com.fs.swap.system', 'system', 'package', '付费评审次数', 'zy', '0', '/', '{}', 'admin', '2025-01-03 13:54:33', '', '2025-01-03 13:57:07', NULL);
INSERT INTO `gen_table` VALUES (6, 'user_votes', '用户投票', NULL, NULL, 'UserVotes', 'crud', 'element-ui', 'com.fs.swap.system', 'system', 'votes', '用户投票', 'zy', '0', '/', '{}', 'admin', '2025-01-03 17:50:24', '', '2025-01-03 17:53:45', NULL);
INSERT INTO `gen_table` VALUES (7, 'cases_comment_likes', '评论点赞表', NULL, NULL, 'CasesCommentLikes', 'crud', 'element-ui', 'com.fs.swap.system', 'system', 'likes', '评论点赞', 'zy', '0', '/', '{}', 'admin', '2025-01-03 18:25:03', '', '2025-01-03 18:25:29', NULL);
INSERT INTO `gen_table` VALUES (8, 'cases_comments', '案件评论表', NULL, NULL, 'CasesComments', 'crud', 'element-ui', 'com.fs.swap.system', 'system', 'comments', '案件评论', 'zy', '0', '/', '{}', 'admin', '2025-01-03 18:25:03', '', '2025-01-03 18:25:45', NULL);
INSERT INTO `gen_table` VALUES (9, 'cases_reply', '案件回复表', NULL, NULL, 'CasesReply', 'crud', 'element-ui', 'com.fs.swap.system', 'system', 'reply', '案件回复', 'zy', '0', '/', '{}', 'admin', '2025-01-08 18:50:26', '', '2025-01-08 18:51:29', NULL);
INSERT INTO `gen_table` VALUES (10, 'user_login_record', '用户登录记录', NULL, NULL, 'UserLoginRecord', 'crud', 'element-ui', 'com.fs.swap.system', 'jury', 'loginRecord', '用户登录记录', 'zy', '0', '/', '{}', 'admin', '2025-01-15 10:45:37', '', '2025-01-15 10:47:48', NULL);
INSERT INTO `gen_table` VALUES (11, 'user_invite_record', '邀请记录', NULL, NULL, 'UserInviteRecord', 'crud', 'element-ui', 'com.fs.swap.system', 'system', 'inviteRecord', '邀请记录', 'zy', '0', '/', '{}', 'admin', '2025-01-16 16:41:03', '', '2025-01-16 16:42:07', NULL);
INSERT INTO `gen_table` VALUES (12, 'user_silver_record', '俸银记录', NULL, NULL, 'UserSilverRecord', 'crud', 'element-ui', 'com.fs.swap.system', 'system', 'record', '俸银记录', 'zy', '0', '/', '{}', 'admin', '2025-01-17 15:12:56', '', '2025-01-17 15:13:31', NULL);
INSERT INTO `gen_table` VALUES (13, 'wx_id_info', '微信id关系表', NULL, NULL, 'WxIdInfo', 'crud', 'element-ui', 'com.fs.swap.system', 'system', 'wxIdInfo', '微信id关系', 'zy', '0', '/', '{\"parentMenuId\":0}', 'admin', '2025-02-08 15:06:00', '', '2025-02-08 15:09:29', NULL);
INSERT INTO `gen_table` VALUES (14, 'constellation_daily_fortune', '星座每日运势', NULL, NULL, 'ConstellationDailyFortune', 'crud', 'element-ui', 'com.fs.swap.system', 'system', 'constellation_daily_fortune', '星座每日运势', 'zy', '0', '/', '{}', 'admin', '2025-02-11 22:32:56', '', '2025-02-11 22:34:25', NULL);

-- ----------------------------
-- Table structure for gen_table_column
-- ----------------------------
DROP TABLE IF EXISTS `gen_table_column`;
CREATE TABLE `gen_table_column`  (
  `column_id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_id` bigint NULL DEFAULT NULL COMMENT '归属表编号',
  `column_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '列名称',
  `column_comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '列描述',
  `column_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '列类型',
  `java_type` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'JAVA类型',
  `java_field` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'JAVA字段名',
  `is_pk` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否主键（1是）',
  `is_increment` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否自增（1是）',
  `is_required` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否必填（1是）',
  `is_insert` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否为插入字段（1是）',
  `is_edit` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否编辑字段（1是）',
  `is_list` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否列表字段（1是）',
  `is_query` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否查询字段（1是）',
  `query_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'EQ' COMMENT '查询方式（等于、不等于、大于、小于、范围）',
  `html_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）',
  `dict_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典类型',
  `sort` int NULL DEFAULT NULL COMMENT '排序',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`column_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 115 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代码生成业务表字段' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of gen_table_column
-- ----------------------------
INSERT INTO `gen_table_column` VALUES (1, 1, 'notice_id', '公告ID', 'int', 'Long', 'noticeId', '1', '1', '0', '1', NULL, NULL, NULL, 'EQ', 'input', '', 1, 'admin', '2024-12-20 15:40:49', '', NULL);
INSERT INTO `gen_table_column` VALUES (2, 1, 'notice_title', '公告标题', 'varchar(50)', 'String', 'noticeTitle', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'input', '', 2, 'admin', '2024-12-20 15:40:49', '', NULL);
INSERT INTO `gen_table_column` VALUES (3, 1, 'notice_type', '公告类型（1通知 2公告）', 'char(1)', 'String', 'noticeType', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'select', '', 3, 'admin', '2024-12-20 15:40:49', '', NULL);
INSERT INTO `gen_table_column` VALUES (4, 1, 'notice_content', '公告内容', 'longblob', 'String', 'noticeContent', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'editor', '', 4, 'admin', '2024-12-20 15:40:49', '', NULL);
INSERT INTO `gen_table_column` VALUES (5, 1, 'status', '公告状态（0正常 1关闭）', 'char(1)', 'String', 'status', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'radio', '', 5, 'admin', '2024-12-20 15:40:49', '', NULL);
INSERT INTO `gen_table_column` VALUES (6, 1, 'create_by', '创建者', 'varchar(64)', 'String', 'createBy', '0', '0', '0', '1', NULL, NULL, NULL, 'EQ', 'input', '', 6, 'admin', '2024-12-20 15:40:49', '', NULL);
INSERT INTO `gen_table_column` VALUES (7, 1, 'create_time', '创建时间', 'datetime', 'Date', 'createTime', '0', '0', '0', '1', NULL, NULL, NULL, 'EQ', 'datetime', '', 7, 'admin', '2024-12-20 15:40:49', '', NULL);
INSERT INTO `gen_table_column` VALUES (8, 1, 'update_by', '更新者', 'varchar(64)', 'String', 'updateBy', '0', '0', '0', '1', '1', NULL, NULL, 'EQ', 'input', '', 8, 'admin', '2024-12-20 15:40:49', '', NULL);
INSERT INTO `gen_table_column` VALUES (9, 1, 'update_time', '更新时间', 'datetime', 'Date', 'updateTime', '0', '0', '0', '1', '1', NULL, NULL, 'EQ', 'datetime', '', 9, 'admin', '2024-12-20 15:40:49', '', NULL);
INSERT INTO `gen_table_column` VALUES (10, 1, 'remark', '备注', 'varchar(255)', 'String', 'remark', '0', '0', '0', '1', '1', '1', NULL, 'EQ', 'input', '', 10, 'admin', '2024-12-20 15:40:49', '', NULL);
INSERT INTO `gen_table_column` VALUES (11, 2, 'id', NULL, 'int', 'Long', 'id', '1', '1', '0', '0', NULL, '1', '1', 'EQ', 'input', '', 1, 'admin', '2024-12-22 10:25:59', '', '2025-02-07 17:17:26');
INSERT INTO `gen_table_column` VALUES (12, 2, 'mobile', '用户手机号码', 'varchar(20)', 'String', 'mobile', '0', '0', '0', '0', '0', '0', '0', 'EQ', 'input', '', 2, 'admin', '2024-12-22 10:25:59', '', '2025-02-07 17:17:26');
INSERT INTO `gen_table_column` VALUES (13, 2, 'openid', '微信登录openid', 'varchar(63)', 'String', 'openid', '0', '0', '0', '0', '0', '0', '0', 'EQ', 'input', '', 3, 'admin', '2024-12-22 10:25:59', '', '2025-02-07 17:17:26');
INSERT INTO `gen_table_column` VALUES (14, 2, 'password', '用户密码', 'varchar(63)', 'String', 'password', '0', '0', '1', '0', '0', '0', '0', 'EQ', 'input', '', 4, 'admin', '2024-12-22 10:25:59', '', '2025-02-07 17:17:26');
INSERT INTO `gen_table_column` VALUES (15, 2, 'nickname', '昵称', 'varchar(63)', 'String', 'nickname', '0', '0', '0', '0', '0', '1', '0', 'LIKE', 'input', '', 5, 'admin', '2024-12-22 10:25:59', '', '2025-02-07 17:17:26');
INSERT INTO `gen_table_column` VALUES (16, 2, 'silver', '俸银', 'int', 'Long', 'silver', '0', '0', '1', '1', '1', '1', '0', 'EQ', 'input', '', 6, 'admin', '2024-12-22 10:25:59', '', '2025-02-07 17:17:27');
INSERT INTO `gen_table_column` VALUES (17, 2, 'avatar', '用户头像图片', 'varchar(255)', 'String', 'avatar', '0', '0', '0', '0', '1', '1', '0', 'EQ', 'imageUpload', '', 7, 'admin', '2024-12-22 10:25:59', '', '2025-02-07 17:17:27');
INSERT INTO `gen_table_column` VALUES (18, 2, 'gender', '性别：0 未知， 1男， 1 女', 'tinyint', 'Long', 'gender', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'select', 'sys_user_sex', 8, 'admin', '2024-12-22 10:25:59', '', '2025-02-07 17:17:27');
INSERT INTO `gen_table_column` VALUES (19, 2, 'birthday', '生日', 'date', 'Date', 'birthday', '0', '0', '0', '0', '0', '1', '0', 'EQ', 'datetime', '', 9, 'admin', '2024-12-22 10:26:00', '', '2025-02-07 17:17:27');
INSERT INTO `gen_table_column` VALUES (20, 2, 'last_login_time', '最近登录时间', 'datetime', 'Date', 'lastLoginTime', '0', '0', '0', '0', '0', '1', '0', 'EQ', 'datetime', '', 10, 'admin', '2024-12-22 10:26:00', '', '2025-02-07 17:17:27');
INSERT INTO `gen_table_column` VALUES (21, 2, 'last_login_ip', '最近登录ip地址', 'varchar(63)', 'String', 'lastLoginIp', '0', '0', '1', '0', '0', '1', '1', 'EQ', 'input', '', 11, 'admin', '2024-12-22 10:26:00', '', '2025-02-07 17:17:27');
INSERT INTO `gen_table_column` VALUES (22, 2, 'user_level', '用户等级', 'tinyint', 'Long', 'userLevel', '0', '0', '0', '1', '1', '1', '0', 'EQ', 'input', '', 12, 'admin', '2024-12-22 10:26:00', '', '2025-02-07 17:17:27');
INSERT INTO `gen_table_column` VALUES (23, 2, 'status', '0 可用, 1 禁用, 2 注销', 'tinyint', 'Long', 'status', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'select', 'sys_common_status', 13, 'admin', '2024-12-22 10:26:00', '', '2025-02-07 17:17:27');
INSERT INTO `gen_table_column` VALUES (24, 2, 'create_time', '创建时间', 'datetime', 'Date', 'createTime', '0', '0', '1', '0', NULL, '1', NULL, 'EQ', 'datetime', '', 14, 'admin', '2024-12-22 10:26:00', '', '2025-02-07 17:17:27');
INSERT INTO `gen_table_column` VALUES (25, 2, 'update_time', '更新时间', 'datetime', 'Date', 'updateTime', '0', '0', '0', '0', '0', '1', NULL, 'EQ', 'datetime', '', 15, 'admin', '2024-12-22 10:26:00', '', '2025-02-07 17:17:27');
INSERT INTO `gen_table_column` VALUES (26, 2, 'deleted', '逻辑删除', 'tinyint(1)', 'Integer', 'deleted', '0', '0', '1', '0', '0', '1', '1', 'EQ', 'select', 'sys_normal_disable', 16, 'admin', '2024-12-22 10:26:00', '', '2025-02-07 17:17:27');
INSERT INTO `gen_table_column` VALUES (27, 3, 'id', 'id', 'int', 'Long', 'id', '1', '1', '0', '0', NULL, '1', '1', 'EQ', 'input', '', 1, 'admin', '2024-12-30 14:16:22', '', '2025-02-10 19:45:47');
INSERT INTO `gen_table_column` VALUES (28, 3, 'title', '标题', 'varchar(255)', 'String', 'title', '0', '0', '0', '0', '0', '1', '1', 'LIKE', 'input', '', 3, 'admin', '2024-12-30 14:16:22', '', '2025-02-10 19:45:47');
INSERT INTO `gen_table_column` VALUES (29, 3, 'description', '描述', 'text', 'String', 'description', '0', '0', '0', '0', '0', '1', '0', 'EQ', 'textarea', '', 4, 'admin', '2024-12-30 14:16:22', '', '2025-02-10 19:45:47');
INSERT INTO `gen_table_column` VALUES (30, 3, 'evidence', '附件', 'text', 'String', 'evidence', '0', '0', '0', '0', '0', '1', '0', 'EQ', 'textarea', '', 10, 'admin', '2024-12-30 14:16:22', '', '2025-02-10 19:45:47');
INSERT INTO `gen_table_column` VALUES (35, 3, 'user_id', 'user_id', 'int', 'Long', 'userId', '0', '0', '0', '0', '0', '1', '1', 'EQ', 'input', '', 2, 'admin', '2024-12-30 14:16:22', '', '2025-02-10 19:45:47');
INSERT INTO `gen_table_column` VALUES (36, 3, 'create_time', '创建时间', 'datetime', 'Date', 'createTime', '0', '0', '0', '0', NULL, '1', NULL, 'EQ', 'datetime', '', 12, 'admin', '2024-12-30 14:16:22', '', '2025-02-10 19:45:47');
INSERT INTO `gen_table_column` VALUES (37, 3, 'update_time', '更新时间', 'datetime', 'Date', 'updateTime', '0', '0', '0', '0', '0', NULL, NULL, 'EQ', 'datetime', '', 13, 'admin', '2024-12-30 14:16:23', '', '2025-02-10 19:45:47');
INSERT INTO `gen_table_column` VALUES (38, 3, 'end_time', '截止时间', 'datetime', 'Date', 'endTime', '0', '0', '0', '0', '0', '1', '0', 'EQ', 'datetime', '', 14, 'admin', '2024-12-30 14:16:23', '', '2025-02-10 19:45:47');
INSERT INTO `gen_table_column` VALUES (39, 3, 'anonymous_is', '是否匿名', 'char(1)', 'String', 'anonymousIs', '0', '0', '0', '0', '0', '1', '1', 'EQ', 'select', 'sys_yes_no', 15, 'admin', '2024-12-30 14:16:23', '', '2025-02-10 19:45:47');
INSERT INTO `gen_table_column` VALUES (40, 3, 'result', '判决结果', 'char(1)', 'String', 'result', '0', '0', '0', '0', '0', '1', '0', 'EQ', 'input', '', 16, 'admin', '2024-12-30 14:16:23', '', '2025-02-10 19:45:47');
INSERT INTO `gen_table_column` VALUES (42, 4, 'id', NULL, 'bigint', 'Long', 'id', '1', '1', '0', '1', NULL, NULL, NULL, 'EQ', 'input', '', 1, 'admin', '2025-01-03 13:54:33', '', '2025-01-03 13:56:31');
INSERT INTO `gen_table_column` VALUES (43, 4, 'user_id', '用户ID', 'bigint', 'Long', 'userId', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'input', '', 2, 'admin', '2025-01-03 13:54:33', '', '2025-01-03 13:56:31');
INSERT INTO `gen_table_column` VALUES (44, 4, 'free_count', '剩余免费次数', 'int', 'Long', 'freeCount', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'input', '', 3, 'admin', '2025-01-03 13:54:33', '', '2025-01-03 13:56:31');
INSERT INTO `gen_table_column` VALUES (45, 4, 'create_time', NULL, 'datetime', 'Date', 'createTime', '0', '0', '1', '1', NULL, NULL, NULL, 'EQ', 'datetime', '', 4, 'admin', '2025-01-03 13:54:33', '', '2025-01-03 13:56:31');
INSERT INTO `gen_table_column` VALUES (46, 5, 'id', NULL, 'bigint', 'Long', 'id', '1', '1', '0', '1', NULL, NULL, NULL, 'EQ', 'input', '', 1, 'admin', '2025-01-03 13:54:33', '', '2025-01-03 13:57:07');
INSERT INTO `gen_table_column` VALUES (47, 5, 'user_id', '用户ID', 'bigint', 'Long', 'userId', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'input', '', 2, 'admin', '2025-01-03 13:54:33', '', '2025-01-03 13:57:07');
INSERT INTO `gen_table_column` VALUES (48, 5, 'remain_count', '剩余次数', 'int', 'Long', 'remainCount', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'input', '', 3, 'admin', '2025-01-03 13:54:33', '', '2025-01-03 13:57:07');
INSERT INTO `gen_table_column` VALUES (49, 5, 'expire_time', '过期时间', 'datetime', 'Date', 'expireTime', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'datetime', '', 4, 'admin', '2025-01-03 13:54:33', '', '2025-01-03 13:57:07');
INSERT INTO `gen_table_column` VALUES (50, 5, 'create_time', NULL, 'datetime', 'Date', 'createTime', '0', '0', '1', '1', NULL, NULL, NULL, 'EQ', 'datetime', '', 5, 'admin', '2025-01-03 13:54:33', '', '2025-01-03 13:57:07');
INSERT INTO `gen_table_column` VALUES (51, 6, 'id', 'id', 'bigint', 'Long', 'id', '1', '1', '0', '1', NULL, NULL, NULL, 'EQ', 'input', '', 1, 'admin', '2025-01-03 17:50:24', '', '2025-01-03 17:53:45');
INSERT INTO `gen_table_column` VALUES (52, 6, 'user_id', '投票用户ID', 'bigint', 'Long', 'userId', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'input', '', 2, 'admin', '2025-01-03 17:50:24', '', '2025-01-03 17:53:45');
INSERT INTO `gen_table_column` VALUES (53, 6, 'cases_id', '案件ID', 'bigint', 'Long', 'casesId', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'input', '', 3, 'admin', '2025-01-03 17:50:24', '', '2025-01-03 17:53:45');
INSERT INTO `gen_table_column` VALUES (54, 6, 'vote_type', '投票类型', 'tinyint', 'String', 'voteType', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'select', '', 4, 'admin', '2025-01-03 17:50:24', '', '2025-01-03 17:53:45');
INSERT INTO `gen_table_column` VALUES (56, 6, 'create_time', '创建时间', 'datetime', 'Date', 'createTime', '0', '0', '1', '1', NULL, NULL, NULL, 'EQ', 'datetime', '', 5, '', '2025-01-03 17:52:13', '', '2025-01-03 17:53:45');
INSERT INTO `gen_table_column` VALUES (57, 7, 'comment_id', '评论ID', 'bigint', 'Long', 'commentId', '1', '0', '0', '1', NULL, NULL, NULL, 'EQ', 'input', '', 1, 'admin', '2025-01-03 18:25:03', '', '2025-01-03 18:25:29');
INSERT INTO `gen_table_column` VALUES (58, 7, 'user_id', '点赞用户ID', 'bigint', 'Long', 'userId', '1', '0', '0', '1', NULL, NULL, NULL, 'EQ', 'input', '', 2, 'admin', '2025-01-03 18:25:03', '', '2025-01-03 18:25:29');
INSERT INTO `gen_table_column` VALUES (59, 7, 'status', '状态：1-已点赞 2-已取消', 'tinyint(1)', 'Integer', 'status', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'radio', '', 3, 'admin', '2025-01-03 18:25:03', '', '2025-01-03 18:25:29');
INSERT INTO `gen_table_column` VALUES (60, 8, 'id', NULL, 'bigint', 'Long', 'id', '1', '1', '0', '1', NULL, NULL, NULL, 'EQ', 'input', '', 1, 'admin', '2025-01-03 18:25:03', '', '2025-01-03 18:25:45');
INSERT INTO `gen_table_column` VALUES (61, 8, 'cases_id', '案件ID', 'bigint', 'Long', 'casesId', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'input', '', 2, 'admin', '2025-01-03 18:25:03', '', '2025-01-03 18:25:45');
INSERT INTO `gen_table_column` VALUES (62, 8, 'user_id', '评论用户ID', 'bigint', 'Long', 'userId', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'input', '', 3, 'admin', '2025-01-03 18:25:03', '', '2025-01-03 18:25:45');
INSERT INTO `gen_table_column` VALUES (63, 8, 'content', '评论内容', 'text', 'String', 'content', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'editor', '', 4, 'admin', '2025-01-03 18:25:03', '', '2025-01-03 18:25:45');
INSERT INTO `gen_table_column` VALUES (64, 8, 'parent_id', '父评论ID，NULL表示一级评论', 'bigint', 'Long', 'parentId', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'input', '', 5, 'admin', '2025-01-03 18:25:03', '', '2025-01-03 18:25:45');
INSERT INTO `gen_table_column` VALUES (65, 8, 'reply_user_id', '被回复的用户ID', 'bigint', 'Long', 'replyUserId', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'input', '', 6, 'admin', '2025-01-03 18:25:03', '', '2025-01-03 18:25:45');
INSERT INTO `gen_table_column` VALUES (66, 8, 'likes_count', '点赞数', 'int', 'Long', 'likesCount', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'input', '', 7, 'admin', '2025-01-03 18:25:03', '', '2025-01-03 18:25:45');
INSERT INTO `gen_table_column` VALUES (67, 8, 'replies_count', '回复数量，仅一级评论有效', 'int', 'Long', 'repliesCount', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'input', '', 8, 'admin', '2025-01-03 18:25:03', '', '2025-01-03 18:25:46');
INSERT INTO `gen_table_column` VALUES (68, 8, 'status', '状态：1-正常 2-用户删除 3-违规删除 4-管理员删除', 'tinyint', 'Long', 'status', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'radio', '', 9, 'admin', '2025-01-03 18:25:03', '', '2025-01-03 18:25:46');
INSERT INTO `gen_table_column` VALUES (69, 8, 'create_at', '创建时间', 'datetime', 'Date', 'createAt', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'datetime', '', 10, 'admin', '2025-01-03 18:25:03', '', '2025-01-03 18:25:46');
INSERT INTO `gen_table_column` VALUES (70, 9, 'id', 'id', 'int', 'Long', 'id', '1', '1', '0', '1', NULL, NULL, NULL, 'EQ', 'input', '', 1, 'admin', '2025-01-08 18:50:26', '', '2025-01-08 18:51:29');
INSERT INTO `gen_table_column` VALUES (71, 9, 'cases_id', 'cases_id', 'int', 'Long', 'casesId', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'input', '', 2, 'admin', '2025-01-08 18:50:26', '', '2025-01-08 18:51:29');
INSERT INTO `gen_table_column` VALUES (72, 9, 'user_id', 'user_id', 'int', 'Long', 'userId', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'input', '', 3, 'admin', '2025-01-08 18:50:26', '', '2025-01-08 18:51:29');
INSERT INTO `gen_table_column` VALUES (73, 9, 'description', '描述', 'text', 'String', 'description', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'textarea', '', 4, 'admin', '2025-01-08 18:50:27', '', '2025-01-08 18:51:29');
INSERT INTO `gen_table_column` VALUES (74, 9, 'evidence', '附件', 'text', 'String', 'evidence', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'textarea', '', 5, 'admin', '2025-01-08 18:50:27', '', '2025-01-08 18:51:29');
INSERT INTO `gen_table_column` VALUES (75, 9, 'status', '状态', 'varchar(10)', 'String', 'status', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'radio', '', 6, 'admin', '2025-01-08 18:50:27', '', '2025-01-08 18:51:29');
INSERT INTO `gen_table_column` VALUES (76, 9, 'create_time', '回复时间', 'datetime', 'Date', 'createTime', '0', '0', '0', '1', NULL, NULL, NULL, 'EQ', 'datetime', '', 7, 'admin', '2025-01-08 18:50:27', '', '2025-01-08 18:51:29');
INSERT INTO `gen_table_column` VALUES (77, 10, 'id', 'id', 'int', 'Long', 'id', '1', '1', '0', '1', NULL, NULL, NULL, 'EQ', 'input', '', 1, 'admin', '2025-01-15 10:45:37', '', '2025-01-15 10:47:48');
INSERT INTO `gen_table_column` VALUES (78, 10, 'user_id', NULL, 'mediumtext', 'String', 'userId', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'textarea', '', 2, 'admin', '2025-01-15 10:45:37', '', '2025-01-15 10:47:48');
INSERT INTO `gen_table_column` VALUES (79, 10, 'ip', NULL, 'varchar(128)', 'String', 'ip', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'input', '', 3, 'admin', '2025-01-15 10:45:37', '', '2025-01-15 10:47:48');
INSERT INTO `gen_table_column` VALUES (80, 10, 'location', NULL, 'varchar(255)', 'String', 'location', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'input', '', 4, 'admin', '2025-01-15 10:45:37', '', '2025-01-15 10:47:49');
INSERT INTO `gen_table_column` VALUES (81, 10, 'create_time', '创建时间', 'datetime', 'Date', 'createTime', '0', '0', '1', '1', NULL, NULL, NULL, 'EQ', 'datetime', '', 5, 'admin', '2025-01-15 10:45:37', '', '2025-01-15 10:47:49');
INSERT INTO `gen_table_column` VALUES (82, 11, 'id', 'id', 'int', 'Long', 'id', '1', '1', '0', '1', NULL, NULL, NULL, 'EQ', 'input', '', 1, 'admin', '2025-01-16 16:41:03', '', '2025-01-16 16:42:07');
INSERT INTO `gen_table_column` VALUES (83, 11, 'inviter_user_id', '邀请人id', 'int', 'Long', 'inviterUserId', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'input', '', 2, 'admin', '2025-01-16 16:41:03', '', '2025-01-16 16:42:07');
INSERT INTO `gen_table_column` VALUES (84, 11, 'invitee_user_id', '被邀请人id', 'int', 'Long', 'inviteeUserId', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'input', '', 3, 'admin', '2025-01-16 16:41:03', '', '2025-01-16 16:42:07');
INSERT INTO `gen_table_column` VALUES (85, 11, 'create_time', '创建时间', 'datetime', 'Date', 'createTime', '0', '0', '1', '1', NULL, NULL, NULL, 'EQ', 'datetime', '', 4, 'admin', '2025-01-16 16:41:03', '', '2025-01-16 16:42:07');
INSERT INTO `gen_table_column` VALUES (86, 12, 'id', 'id', 'int', 'Long', 'id', '1', '0', '0', '1', NULL, NULL, NULL, 'EQ', 'input', '', 1, 'admin', '2025-01-17 15:12:56', '', '2025-01-17 15:13:31');
INSERT INTO `gen_table_column` VALUES (87, 12, 'user_id', 'user_id', 'int', 'Long', 'userId', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'input', '', 2, 'admin', '2025-01-17 15:12:56', '', '2025-01-17 15:13:31');
INSERT INTO `gen_table_column` VALUES (88, 12, 'event', '事件', 'varchar(10)', 'String', 'event', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'input', '', 3, 'admin', '2025-01-17 15:12:56', '', '2025-01-17 15:13:31');
INSERT INTO `gen_table_column` VALUES (89, 12, 'type', '类型', 'char(1)', 'String', 'type', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'select', '', 4, 'admin', '2025-01-17 15:12:56', '', '2025-01-17 15:13:31');
INSERT INTO `gen_table_column` VALUES (90, 12, 'number', '数量', 'int', 'Long', 'number', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'input', '', 5, 'admin', '2025-01-17 15:12:56', '', '2025-01-17 15:13:31');
INSERT INTO `gen_table_column` VALUES (91, 12, 'create_time', '创建事件', 'datetime', 'Date', 'createTime', '0', '0', '1', '1', NULL, NULL, NULL, 'EQ', 'datetime', '', 6, 'admin', '2025-01-17 15:12:57', '', '2025-01-17 15:13:31');
INSERT INTO `gen_table_column` VALUES (92, 13, 'union_id', 'unionId', 'varchar(64)', 'String', 'unionId', '1', '0', '0', '1', NULL, NULL, NULL, 'EQ', 'input', '', 1, 'admin', '2025-02-08 15:06:00', '', '2025-02-08 15:09:29');
INSERT INTO `gen_table_column` VALUES (93, 13, 'ma_open_id', '小程序openId', 'varchar(64)', 'String', 'maOpenId', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'input', '', 2, 'admin', '2025-02-08 15:06:00', '', '2025-02-08 15:09:29');
INSERT INTO `gen_table_column` VALUES (94, 13, 'mp_open_id', '公众号openId', 'varchar(64)', 'String', 'mpOpenId', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'input', '', 3, 'admin', '2025-02-08 15:06:00', '', '2025-02-08 15:09:29');
INSERT INTO `gen_table_column` VALUES (95, 3, 'summary', 'AI总结', 'varchar(300)', 'String', 'summary', '0', '0', '0', '0', '0', '1', '0', 'EQ', 'textarea', '', 5, '', '2025-02-10 19:35:58', '', '2025-02-10 19:45:47');
INSERT INTO `gen_table_column` VALUES (96, 3, 'approve_text', '支持文本', 'varchar(6)', 'String', 'approveText', '0', '0', '0', '0', '0', '1', '1', 'EQ', 'input', '', 6, '', '2025-02-10 19:35:58', '', '2025-02-10 19:45:47');
INSERT INTO `gen_table_column` VALUES (97, 3, 'against_text', '反对文本', 'varchar(6)', 'String', 'againstText', '0', '0', '0', '0', '0', '1', '1', 'EQ', 'input', '', 7, '', '2025-02-10 19:35:58', '', '2025-02-10 19:45:47');
INSERT INTO `gen_table_column` VALUES (98, 3, 'type', '类型', 'varchar(20)', 'String', 'type', '0', '0', '0', '0', '0', '1', '1', 'EQ', 'select', 'cases_type', 8, '', '2025-02-10 19:35:58', '', '2025-02-10 19:45:47');
INSERT INTO `gen_table_column` VALUES (99, 3, 'labels', '标签', 'varchar(20)', 'String', 'labels', '0', '0', '0', '0', '0', '1', '0', 'EQ', 'input', '', 9, '', '2025-02-10 19:35:58', '', '2025-02-10 19:45:47');
INSERT INTO `gen_table_column` VALUES (100, 3, 'invite_status', '邀请状态', 'char(1)', 'String', 'inviteStatus', '0', '0', '0', '0', '0', '1', '0', 'EQ', 'select', '', 11, '', '2025-02-10 19:35:58', '', '2025-02-10 19:45:47');
INSERT INTO `gen_table_column` VALUES (101, 3, 'boost_expire_time', '加速截止时间', 'datetime', 'Date', 'boostExpireTime', '0', '0', '0', '0', '0', '1', '0', 'EQ', 'datetime', '', 17, '', '2025-02-10 19:35:58', '', '2025-02-10 19:45:47');
INSERT INTO `gen_table_column` VALUES (102, 3, 'deleted', '是否删除', 'tinyint', 'Long', 'deleted', '0', '0', '0', '0', '0', '1', '1', 'EQ', 'select', 'sys_yes_no', 18, '', '2025-02-10 19:35:58', '', '2025-02-10 19:45:47');
INSERT INTO `gen_table_column` VALUES (103, 14, 'id', 'id', 'int', 'Long', 'id', '1', '1', '0', '1', NULL, NULL, NULL, 'EQ', 'input', '', 1, 'admin', '2025-02-11 22:32:56', '', '2025-02-11 22:34:25');
INSERT INTO `gen_table_column` VALUES (104, 14, 'constellation', '星座', 'varchar(10)', 'String', 'constellation', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'input', '', 2, 'admin', '2025-02-11 22:32:56', '', '2025-02-11 22:34:25');
INSERT INTO `gen_table_column` VALUES (105, 14, 'date', '日期', 'date', 'Date', 'date', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'datetime', '', 3, 'admin', '2025-02-11 22:32:56', '', '2025-02-11 22:34:25');
INSERT INTO `gen_table_column` VALUES (106, 14, 'love', '爱情指数', 'int', 'Long', 'love', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'input', '', 4, 'admin', '2025-02-11 22:32:56', '', '2025-02-11 22:34:25');
INSERT INTO `gen_table_column` VALUES (107, 14, 'career', '事业指数', 'int', 'Long', 'career', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'input', '', 5, 'admin', '2025-02-11 22:32:56', '', '2025-02-11 22:34:25');
INSERT INTO `gen_table_column` VALUES (108, 14, 'health', '健康指数', 'int', 'Long', 'health', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'input', '', 6, 'admin', '2025-02-11 22:32:56', '', '2025-02-11 22:34:26');
INSERT INTO `gen_table_column` VALUES (109, 14, 'money', '财运指数', 'int', 'Long', 'money', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'input', '', 7, 'admin', '2025-02-11 22:32:56', '', '2025-02-11 22:34:26');
INSERT INTO `gen_table_column` VALUES (110, 14, 'speed_dating', '速配星座', 'varchar(10)', 'String', 'speedDating', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'input', '', 8, 'admin', '2025-02-11 22:32:56', '', '2025-02-11 22:34:26');
INSERT INTO `gen_table_column` VALUES (111, 14, 'lucky_number', '幸运数字', 'int', 'Long', 'luckyNumber', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'input', '', 9, 'admin', '2025-02-11 22:32:56', '', '2025-02-11 22:34:26');
INSERT INTO `gen_table_column` VALUES (112, 14, 'summary', '运势概述', 'text', 'String', 'summary', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'textarea', '', 10, 'admin', '2025-02-11 22:32:56', '', '2025-02-11 22:34:26');
INSERT INTO `gen_table_column` VALUES (113, 14, 'created_time', '创建时间', 'datetime', 'Date', 'createdTime', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'datetime', '', 11, 'admin', '2025-02-11 22:32:56', '', '2025-02-11 22:34:26');
INSERT INTO `gen_table_column` VALUES (114, 14, 'updated_time', '更新时间', 'datetime', 'Date', 'updatedTime', '0', '0', '0', '1', '1', '1', '1', 'EQ', 'datetime', '', 12, 'admin', '2025-02-11 22:32:56', '', '2025-02-11 22:34:26');

-- ----------------------------
-- Table structure for qrtz_blob_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_blob_triggers`;
CREATE TABLE `qrtz_blob_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `blob_data` blob NULL COMMENT '存放持久化Trigger对象',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_blob_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'Blob类型的触发器表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_blob_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_calendars
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_calendars`;
CREATE TABLE `qrtz_calendars`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `calendar_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '日历名称',
  `calendar` blob NOT NULL COMMENT '存放持久化calendar对象',
  PRIMARY KEY (`sched_name`, `calendar_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '日历信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_calendars
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_cron_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_cron_triggers`;
CREATE TABLE `qrtz_cron_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `cron_expression` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'cron表达式',
  `time_zone_id` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '时区',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_cron_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'Cron类型的触发器表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_cron_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_fired_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_fired_triggers`;
CREATE TABLE `qrtz_fired_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `entry_id` varchar(95) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度器实例id',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `instance_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度器实例名',
  `fired_time` bigint NOT NULL COMMENT '触发的时间',
  `sched_time` bigint NOT NULL COMMENT '定时器制定的时间',
  `priority` int NOT NULL COMMENT '优先级',
  `state` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '状态',
  `job_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务名称',
  `job_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务组名',
  `is_nonconcurrent` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否并发',
  `requests_recovery` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否接受恢复执行',
  PRIMARY KEY (`sched_name`, `entry_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '已触发的触发器表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_fired_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_job_details
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_job_details`;
CREATE TABLE `qrtz_job_details`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `job_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务名称',
  `job_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务组名',
  `description` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '相关介绍',
  `job_class_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '执行任务类名称',
  `is_durable` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '是否持久化',
  `is_nonconcurrent` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '是否并发',
  `is_update_data` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '是否更新数据',
  `requests_recovery` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '是否接受恢复执行',
  `job_data` blob NULL COMMENT '存放持久化job对象',
  PRIMARY KEY (`sched_name`, `job_name`, `job_group`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '任务详细信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_job_details
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_locks
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_locks`;
CREATE TABLE `qrtz_locks`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `lock_name` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '悲观锁名称',
  PRIMARY KEY (`sched_name`, `lock_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '存储的悲观锁信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_locks
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_paused_trigger_grps
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_paused_trigger_grps`;
CREATE TABLE `qrtz_paused_trigger_grps`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  PRIMARY KEY (`sched_name`, `trigger_group`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '暂停的触发器表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_paused_trigger_grps
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_scheduler_state
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_scheduler_state`;
CREATE TABLE `qrtz_scheduler_state`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `instance_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '实例名称',
  `last_checkin_time` bigint NOT NULL COMMENT '上次检查时间',
  `checkin_interval` bigint NOT NULL COMMENT '检查间隔时间',
  PRIMARY KEY (`sched_name`, `instance_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '调度器状态表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_scheduler_state
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_simple_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_simple_triggers`;
CREATE TABLE `qrtz_simple_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `repeat_count` bigint NOT NULL COMMENT '重复的次数统计',
  `repeat_interval` bigint NOT NULL COMMENT '重复的间隔时间',
  `times_triggered` bigint NOT NULL COMMENT '已经触发的次数',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_simple_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '简单触发器的信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_simple_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_simprop_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_simprop_triggers`;
CREATE TABLE `qrtz_simprop_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `str_prop_1` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'String类型的trigger的第一个参数',
  `str_prop_2` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'String类型的trigger的第二个参数',
  `str_prop_3` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'String类型的trigger的第三个参数',
  `int_prop_1` int NULL DEFAULT NULL COMMENT 'int类型的trigger的第一个参数',
  `int_prop_2` int NULL DEFAULT NULL COMMENT 'int类型的trigger的第二个参数',
  `long_prop_1` bigint NULL DEFAULT NULL COMMENT 'long类型的trigger的第一个参数',
  `long_prop_2` bigint NULL DEFAULT NULL COMMENT 'long类型的trigger的第二个参数',
  `dec_prop_1` decimal(13, 4) NULL DEFAULT NULL COMMENT 'decimal类型的trigger的第一个参数',
  `dec_prop_2` decimal(13, 4) NULL DEFAULT NULL COMMENT 'decimal类型的trigger的第二个参数',
  `bool_prop_1` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'Boolean类型的trigger的第一个参数',
  `bool_prop_2` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'Boolean类型的trigger的第二个参数',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_simprop_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '同步机制的行锁表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_simprop_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_triggers`;
CREATE TABLE `qrtz_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '触发器的名字',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '触发器所属组的名字',
  `job_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_job_details表job_name的外键',
  `job_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_job_details表job_group的外键',
  `description` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '相关介绍',
  `next_fire_time` bigint NULL DEFAULT NULL COMMENT '上一次触发时间（毫秒）',
  `prev_fire_time` bigint NULL DEFAULT NULL COMMENT '下一次触发时间（默认为-1表示不触发）',
  `priority` int NULL DEFAULT NULL COMMENT '优先级',
  `trigger_state` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '触发器状态',
  `trigger_type` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '触发器的类型',
  `start_time` bigint NOT NULL COMMENT '开始时间',
  `end_time` bigint NULL DEFAULT NULL COMMENT '结束时间',
  `calendar_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '日程表名称',
  `misfire_instr` smallint NULL DEFAULT NULL COMMENT '补偿执行的策略',
  `job_data` blob NULL COMMENT '存放持久化job对象',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  INDEX `sched_name`(`sched_name` ASC, `job_name` ASC, `job_group` ASC) USING BTREE,
  CONSTRAINT `qrtz_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `job_name`, `job_group`) REFERENCES `qrtz_job_details` (`sched_name`, `job_name`, `job_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '触发器详细信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config`  (
  `config_id` int NOT NULL AUTO_INCREMENT COMMENT '参数主键',
  `config_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '参数名称',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '参数键名',
  `config_value` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '参数键值',
  `config_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 103 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '参数配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_config
-- ----------------------------
INSERT INTO `sys_config` VALUES (1, '主框架页-默认皮肤样式名称', 'sys.index.skinName', 'skin-blue', 'Y', 'admin', '2024-12-18 14:06:57', '', NULL, '蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow');
INSERT INTO `sys_config` VALUES (2, '用户管理-账号初始密码', 'sys.user.initPassword', '123456', 'Y', 'admin', '2024-12-18 14:06:57', '', NULL, '初始化密码 123456');
INSERT INTO `sys_config` VALUES (3, '主框架页-侧边栏主题', 'sys.index.sideTheme', 'theme-dark', 'Y', 'admin', '2024-12-18 14:06:57', '', NULL, '深色主题theme-dark，浅色主题theme-light');
INSERT INTO `sys_config` VALUES (4, '账号自助-验证码开关', 'sys.account.captchaEnabled', 'true', 'Y', 'admin', '2024-12-18 14:06:57', '', NULL, '是否开启验证码功能（true开启，false关闭）');
INSERT INTO `sys_config` VALUES (5, '账号自助-是否开启用户注册功能', 'sys.account.registerUser', 'false', 'Y', 'admin', '2024-12-18 14:06:57', '', NULL, '是否开启注册用户功能（true开启，false关闭）');
INSERT INTO `sys_config` VALUES (6, '用户登录-黑名单列表', 'sys.login.blackIPList', '', 'Y', 'admin', '2024-12-18 14:06:57', '', NULL, '设置登录IP黑名单限制，多个匹配项以;分隔，支持匹配（*通配、网段）');
INSERT INTO `sys_config` VALUES (100, '标签推荐', 'jury.tag.recommend', '{ \"1\": [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"], \"2\": [\"8\", \"9\", \"10\", \"11\", \"12\", \"13\", \"14\"], \"3\": [\"15\", \"16\", \"17\", \"18\", \"19\", \"20\", \"21\"], \"4\": [\"22\", \"23\", \"24\", \"25\", \"26\", \"27\", \"28\"], \"5\": [\"29\", \"30\", \"31\", \"32\", \"33\", \"34\", \"35\"], \"6\": [\"36\", \"37\", \"38\", \"39\", \"40\", \"41\", \"42\"], \"7\": [\"43\", \"44\", \"45\", \"46\", \"47\", \"48\", \"49\"], \"8\": [\"50\", \"51\", \"52\", \"53\", \"54\", \"55\", \"56\"], \"9\": [\"57\", \"58\", \"59\", \"60\", \"61\", \"62\"], \"10\": [\"63\", \"64\", \"65\", \"66\", \"67\", \"68\",\"69\"], \"11\": [\"70\", \"71\", \"72\", \"73\", \"74\", \"75\", \"76\"] }', 'Y', 'admin', '2025-01-01 08:28:51', 'admin', '2025-01-03 15:45:50', '根据类型推荐标签');
INSERT INTO `sys_config` VALUES (101, '购买1次评审所消耗俸银', 'jury.review.silver', '50', 'Y', 'admin', '2025-01-03 15:44:08', 'admin', '2025-01-31 20:46:22', NULL);
INSERT INTO `sys_config` VALUES (102, '用户每日可发布案件数', 'jury.everyday.publish.count', '5', 'Y', 'admin', '2025-01-19 16:58:40', 'admin', '2025-02-06 14:03:48', NULL);

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept`  (
  `dept_id` bigint NOT NULL AUTO_INCREMENT COMMENT '部门id',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父部门id',
  `ancestors` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '祖级列表',
  `dept_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '部门名称',
  `order_num` int NULL DEFAULT 0 COMMENT '显示顺序',
  `leader` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '负责人',
  `phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`dept_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 110 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '部门表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dept
-- ----------------------------
INSERT INTO `sys_dept` VALUES (100, 0, '0', '卡皮巴拉', 0, '周野', '17326180826', '<EMAIL>', '0', '0', 'admin', '2024-12-18 14:06:26', 'admin', '2025-02-07 16:32:29');
INSERT INTO `sys_dept` VALUES (101, 100, '0,100', '深圳总公司', 1, '若依', '***********', '<EMAIL>', '0', '2', 'admin', '2024-12-18 14:06:26', '', NULL);
INSERT INTO `sys_dept` VALUES (102, 100, '0,100', '长沙分公司', 2, '若依', '***********', '<EMAIL>', '0', '2', 'admin', '2024-12-18 14:06:26', '', NULL);
INSERT INTO `sys_dept` VALUES (103, 100, '0,100', '研发部门', 1, '周野', '17326180826', '<EMAIL>', '0', '0', 'admin', '2024-12-18 14:06:26', 'admin', '2025-02-07 16:33:58');
INSERT INTO `sys_dept` VALUES (104, 101, '0,100,101', '市场部门', 2, '若依', '***********', '<EMAIL>', '0', '2', 'admin', '2024-12-18 14:06:26', '', NULL);
INSERT INTO `sys_dept` VALUES (105, 100, '0,100', '运营部门', 2, '周野', '17326180826', '<EMAIL>', '0', '0', 'admin', '2024-12-18 14:06:26', 'admin', '2025-02-07 16:34:52');
INSERT INTO `sys_dept` VALUES (106, 101, '0,100,101', '财务部门', 4, '若依', '***********', '<EMAIL>', '0', '2', 'admin', '2024-12-18 14:06:26', '', NULL);
INSERT INTO `sys_dept` VALUES (107, 101, '0,100,101', '运维部门', 5, '若依', '***********', '<EMAIL>', '0', '2', 'admin', '2024-12-18 14:06:26', '', NULL);
INSERT INTO `sys_dept` VALUES (108, 102, '0,100,102', '市场部门', 1, '若依', '***********', '<EMAIL>', '0', '2', 'admin', '2024-12-18 14:06:27', '', NULL);
INSERT INTO `sys_dept` VALUES (109, 102, '0,100,102', '财务部门', 2, '若依', '***********', '<EMAIL>', '0', '2', 'admin', '2024-12-18 14:06:27', '', NULL);

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_data`;
CREATE TABLE `sys_dict_data`  (
  `dict_code` bigint NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  `dict_sort` int NULL DEFAULT 0 COMMENT '字典排序',
  `dict_label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典键值',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典类型',
  `css_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  `list_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表格回显样式',
  `is_default` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 222 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '字典数据表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dict_data
-- ----------------------------
INSERT INTO `sys_dict_data` VALUES (1, 1, '未知', '0', 'sys_user_sex', '', 'info', 'Y', '0', 'admin', '2024-12-18 14:06:53', 'admin', '2025-02-07 17:22:18', '性别未知');
INSERT INTO `sys_dict_data` VALUES (2, 1, '男', '1', 'sys_user_sex', '', 'primary', 'N', '0', 'admin', '2024-12-18 14:06:53', 'admin', '2025-02-07 17:21:58', '性别男');
INSERT INTO `sys_dict_data` VALUES (3, 2, '女', '2', 'sys_user_sex', '', 'primary', 'N', '0', 'admin', '2024-12-18 14:06:53', 'admin', '2025-02-07 17:22:11', '性别女');
INSERT INTO `sys_dict_data` VALUES (4, 1, '显示', '0', 'sys_show_hide', '', 'primary', 'Y', '0', 'admin', '2024-12-18 14:06:53', '', NULL, '显示菜单');
INSERT INTO `sys_dict_data` VALUES (5, 2, '隐藏', '1', 'sys_show_hide', '', 'danger', 'N', '0', 'admin', '2024-12-18 14:06:54', '', NULL, '隐藏菜单');
INSERT INTO `sys_dict_data` VALUES (6, 1, '正常', '0', 'sys_normal_disable', '', 'primary', 'Y', '0', 'admin', '2024-12-18 14:06:54', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (7, 2, '停用', '1', 'sys_normal_disable', '', 'danger', 'N', '0', 'admin', '2024-12-18 14:06:54', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (8, 1, '正常', '0', 'sys_job_status', '', 'primary', 'Y', '0', 'admin', '2024-12-18 14:06:54', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (9, 2, '暂停', '1', 'sys_job_status', '', 'danger', 'N', '0', 'admin', '2024-12-18 14:06:54', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (10, 1, '默认', 'DEFAULT', 'sys_job_group', '', '', 'Y', '0', 'admin', '2024-12-18 14:06:54', '', NULL, '默认分组');
INSERT INTO `sys_dict_data` VALUES (11, 2, '系统', 'SYSTEM', 'sys_job_group', '', '', 'N', '0', 'admin', '2024-12-18 14:06:54', '', NULL, '系统分组');
INSERT INTO `sys_dict_data` VALUES (12, 1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', '0', 'admin', '2024-12-18 14:06:54', '', NULL, '系统默认是');
INSERT INTO `sys_dict_data` VALUES (13, 2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', '0', 'admin', '2024-12-18 14:06:55', '', NULL, '系统默认否');
INSERT INTO `sys_dict_data` VALUES (14, 1, '通知', '1', 'sys_notice_type', '', 'warning', 'Y', '0', 'admin', '2024-12-18 14:06:55', '', NULL, '通知');
INSERT INTO `sys_dict_data` VALUES (15, 2, '公告', '2', 'sys_notice_type', '', 'success', 'N', '0', 'admin', '2024-12-18 14:06:55', '', NULL, '公告');
INSERT INTO `sys_dict_data` VALUES (16, 1, '正常', '0', 'sys_notice_status', '', 'primary', 'Y', '0', 'admin', '2024-12-18 14:06:55', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (17, 2, '关闭', '1', 'sys_notice_status', '', 'danger', 'N', '0', 'admin', '2024-12-18 14:06:55', '', NULL, '关闭状态');
INSERT INTO `sys_dict_data` VALUES (18, 99, '其他', '0', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2024-12-18 14:06:55', '', NULL, '其他操作');
INSERT INTO `sys_dict_data` VALUES (19, 1, '新增', '1', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2024-12-18 14:06:55', '', NULL, '新增操作');
INSERT INTO `sys_dict_data` VALUES (20, 2, '修改', '2', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2024-12-18 14:06:55', '', NULL, '修改操作');
INSERT INTO `sys_dict_data` VALUES (21, 3, '删除', '3', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2024-12-18 14:06:55', '', NULL, '删除操作');
INSERT INTO `sys_dict_data` VALUES (22, 4, '授权', '4', 'sys_oper_type', '', 'primary', 'N', '0', 'admin', '2024-12-18 14:06:56', '', NULL, '授权操作');
INSERT INTO `sys_dict_data` VALUES (23, 5, '导出', '5', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2024-12-18 14:06:56', '', NULL, '导出操作');
INSERT INTO `sys_dict_data` VALUES (24, 6, '导入', '6', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2024-12-18 14:06:56', '', NULL, '导入操作');
INSERT INTO `sys_dict_data` VALUES (25, 7, '强退', '7', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2024-12-18 14:06:56', '', NULL, '强退操作');
INSERT INTO `sys_dict_data` VALUES (26, 8, '生成代码', '8', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2024-12-18 14:06:56', '', NULL, '生成操作');
INSERT INTO `sys_dict_data` VALUES (27, 9, '清空数据', '9', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2024-12-18 14:06:56', '', NULL, '清空操作');
INSERT INTO `sys_dict_data` VALUES (28, 1, '成功', '0', 'sys_common_status', '', 'primary', 'N', '0', 'admin', '2024-12-18 14:06:56', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (29, 2, '失败', '1', 'sys_common_status', '', 'danger', 'N', '0', 'admin', '2024-12-18 14:06:56', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (100, 1, '恋爱矛盾', '1', 'cases_type', NULL, 'default', 'N', '0', 'admin', '2024-12-31 10:59:25', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (101, 2, '日常琐事', '2', 'cases_type', NULL, 'default', 'N', '0', 'admin', '2024-12-31 10:59:47', 'admin', '2024-12-31 11:00:44', NULL);
INSERT INTO `sys_dict_data` VALUES (102, 3, '金钱与消费', '3', 'cases_type', NULL, 'default', 'N', '0', 'admin', '2024-12-31 10:59:58', 'admin', '2024-12-31 11:00:52', NULL);
INSERT INTO `sys_dict_data` VALUES (103, 4, '节日与纪念日', '4', 'cases_type', NULL, 'default', 'N', '0', 'admin', '2024-12-31 11:00:22', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (104, 5, '约会与出行', '5', 'cases_type', NULL, 'default', 'N', '0', 'admin', '2024-12-31 11:01:26', 'admin', '2024-12-31 11:01:30', NULL);
INSERT INTO `sys_dict_data` VALUES (105, 6, '沟通问题', '6', 'cases_type', NULL, 'default', 'N', '0', 'admin', '2024-12-31 11:01:42', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (106, 7, '亲密关系', '7', 'cases_type', NULL, 'default', 'N', '0', 'admin', '2024-12-31 11:02:01', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (107, 8, '家庭与朋友关系', '8', 'cases_type', NULL, 'default', 'N', '0', 'admin', '2024-12-31 11:02:11', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (108, 9, '工作与生活平衡', '9', 'cases_type', NULL, 'default', 'N', '0', 'admin', '2024-12-31 11:02:28', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (109, 10, '分手相关', '10', 'cases_type', NULL, 'default', 'N', '0', 'admin', '2024-12-31 11:02:39', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (110, 11, ' 特殊事件', '11', 'cases_type', NULL, 'default', 'N', '0', 'admin', '2024-12-31 11:02:53', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (111, 1, '忽冷忽热', '1', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:35:28', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (112, 2, '太过黏人', '2', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:35:37', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (113, 3, '吃醋', '3', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:35:46', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (114, 4, '不信任', '4', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:35:53', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (115, 5, '太少沟通', '5', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:36:03', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (116, 6, '情绪化', '6', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:36:11', 'admin', '2024-12-31 13:36:19', NULL);
INSERT INTO `sys_dict_data` VALUES (117, 7, '不主动联系', '7', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:36:31', 'admin', '2024-12-31 13:36:36', NULL);
INSERT INTO `sys_dict_data` VALUES (118, 8, '家务分配', '8', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:37:00', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (119, 9, '吵架', '9', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:37:12', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (120, 10, '乱扔东西', '10', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:37:22', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (121, 11, '洗碗问题', '11', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:37:34', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (122, 12, '清洁习惯', '12', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:37:45', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (123, 13, '作息不一致', '13', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:37:57', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (124, 14, '借东西不还', '14', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:38:05', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (125, 15, '开销分配', '15', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:39:58', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (126, 16, '花钱太多', '16', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:40:17', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (127, 17, '不愿承担', '17', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:40:31', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (128, 18, '礼物预算', '18', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:40:42', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (129, 19, '借钱不还', '19', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:40:53', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (130, 20, '不存钱', '20', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:41:04', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (131, 21, '奢侈消费', '21', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:41:14', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (132, 22, '忘记纪念日', '22', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:41:27', 'admin', '2024-12-31 13:41:34', NULL);
INSERT INTO `sys_dict_data` VALUES (133, 23, '礼物不合适', '23', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:41:44', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (134, 24, '计划失败', '24', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:41:54', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (135, 25, '没有准备', '25', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:42:03', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (136, 26, '节日冷淡', '26', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:42:15', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (137, 27, '行程冲突', '27', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:42:26', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (138, 28, '没有仪式感', '28', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:42:36', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (139, 29, '迟到', '29', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:42:51', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (140, 30, '手机上瘾', '30', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:43:07', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (141, 31, '目的地选择', '31', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:43:17', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (142, 32, '行程安排', '32', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:43:33', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (143, 33, '不守时', '33', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:43:47', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (144, 34, '出行分歧', '34', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:43:58', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (145, 35, '旅行预算', '35', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:44:09', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (146, 36, '冷战', '36', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:44:28', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (147, 37, '情绪爆发', '37', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:44:44', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (148, 38, '太少沟通', '38', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:45:07', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (149, 39, '太直接', '39', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:45:21', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (150, 40, '不回应', '40', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:45:31', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (151, 41, '含糊其辞', '41', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:45:40', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (152, 42, '逃避问题', '42', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:45:51', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (153, 43, '太过黏人', '43', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:46:09', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (154, 44, '疏远', '44', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:46:25', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (155, 45, '不愿接触', '45', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:46:33', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (156, 46, '缺乏关心', '46', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:46:44', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (157, 47, '不愿分享', '47', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:46:53', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (158, 48, '不给空间', '48', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:47:07', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (159, 49, '亲密度下降', '49', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:47:18', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (160, 50, '父母干涉', '50', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:47:42', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (161, 51, '朋友优先', '51', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:47:56', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (162, 52, '不愿见家人', '52', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:48:06', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (163, 53, '忽略朋友圈', '53', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:48:16', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (164, 54, '亲友冲突', '54', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:48:24', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (165, 55, '父母不支持', '55', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:48:34', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (166, 56, '聚会太频繁', '56', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:48:44', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (167, 57, '忙于工作', '57', 'cases_label', '', 'default', 'N', '0', 'admin', '2024-12-31 13:48:54', 'admin', '2024-12-31 13:51:33', NULL);
INSERT INTO `sys_dict_data` VALUES (168, 58, '忽略感情', '58', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:49:04', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (169, 59, '下班打游戏', '59', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:49:17', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (170, 60, '压力大', '60', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:49:28', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (171, 61, '时间不足', '61', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:49:43', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (172, 62, '加班过多', '62', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:50:09', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (173, 63, '提出分手', '63', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:50:21', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (174, 64, '挽回失败', '64', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:50:30', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (175, 65, '不愿分开', '65', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:50:38', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (176, 66, '分手后纠缠', '66', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:50:48', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (177, 67, '分手理由', '67', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:50:59', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (178, 68, '感情淡了', '68', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:51:11', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (179, 69, '纠结继续', '69', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:51:19', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (180, 70, '突发情况', '70', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:52:28', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (181, 71, '难以原谅', '71', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:52:42', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (182, 72, '短暂争执', '72', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:52:56', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (183, 73, '突然变故', '73', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:53:06', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (184, 74, '不在状态', '74', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:53:18', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (185, 75, '关系紧张', '75', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:53:26', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (186, 76, '临时问题', '76', 'cases_label', NULL, 'default', 'N', '0', 'admin', '2024-12-31 13:53:37', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (187, 1, 'airplane', 'airplane', 'sys_avatar', NULL, 'default', 'N', '0', 'admin', '2025-02-01 21:39:07', 'admin', '2025-02-01 21:40:07', NULL);
INSERT INTO `sys_dict_data` VALUES (188, 2, 'apple', 'apple', 'sys_avatar', NULL, 'default', 'N', '0', 'admin', '2025-02-01 21:39:20', 'admin', '2025-02-01 21:40:13', NULL);
INSERT INTO `sys_dict_data` VALUES (189, 3, 'book', 'book', 'sys_avatar', NULL, 'default', 'N', '0', 'admin', '2025-02-01 21:39:34', 'admin', '2025-02-01 21:40:18', NULL);
INSERT INTO `sys_dict_data` VALUES (190, 4, 'broccoli', 'broccoli', 'sys_avatar', NULL, 'default', 'N', '0', 'admin', '2025-02-01 21:40:31', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (191, 5, 'camera', 'camera', 'sys_avatar', '', 'default', 'N', '0', 'admin', '2025-02-01 21:40:41', 'admin', '2025-02-01 21:40:56', NULL);
INSERT INTO `sys_dict_data` VALUES (192, 6, 'cat', 'cat', 'sys_avatar', NULL, 'default', 'N', '0', 'admin', '2025-02-01 21:41:08', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (193, 7, 'chili', 'chili', 'sys_avatar', NULL, 'default', 'N', '0', 'admin', '2025-02-01 21:41:18', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (194, 8, 'drum', 'drum', 'sys_avatar', NULL, 'default', 'N', '0', 'admin', '2025-02-01 21:41:28', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (195, 9, 'duck', 'duck', 'sys_avatar', NULL, 'default', 'N', '0', 'admin', '2025-02-01 21:41:41', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (196, 10, 'hat', 'hat', 'sys_avatar', NULL, 'default', 'N', '0', 'admin', '2025-02-01 21:41:51', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (197, 11, 'headphones', 'headphones', 'sys_avatar', '', 'default', 'N', '0', 'admin', '2025-02-01 21:41:59', 'admin', '2025-02-01 21:42:41', NULL);
INSERT INTO `sys_dict_data` VALUES (198, 12, 'letter', 'letter', 'sys_avatar', NULL, 'default', 'N', '0', 'admin', '2025-02-01 21:42:11', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (199, 13, 'mushroom', 'mushroom', 'sys_avatar', NULL, 'default', 'N', '0', 'admin', '2025-02-01 21:42:22', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (200, 14, 'pizza', 'pizza', 'sys_avatar', NULL, 'default', 'N', '0', 'admin', '2025-02-01 21:42:32', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (201, 15, 'saturn', 'saturn', 'sys_avatar', NULL, 'default', 'N', '0', 'admin', '2025-02-01 21:42:55', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (202, 16, 'shoes', 'shoes', 'sys_avatar', NULL, 'default', 'N', '0', 'admin', '2025-02-01 21:43:07', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (203, 17, 'sweater', 'sweater', 'sys_avatar', NULL, 'default', 'N', '0', 'admin', '2025-02-01 21:43:18', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (204, 18, 'tent', 'tent', 'sys_avatar', NULL, 'default', 'N', '0', 'admin', '2025-02-01 21:43:29', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (205, 19, 'wangzai', 'wangzai', 'sys_avatar', NULL, 'default', 'N', '0', 'admin', '2025-02-01 21:43:38', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (206, 20, 'watermelon', 'watermelon', 'sys_avatar', NULL, 'default', 'N', '0', 'admin', '2025-02-01 21:43:48', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (207, 0, '正常', '0', 'user_status', NULL, 'success', 'N', '0', 'admin', '2025-02-07 16:20:00', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (208, 1, '禁用', '1', 'user_status', NULL, 'danger', 'N', '0', 'admin', '2025-02-07 16:20:16', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (209, 2, '注销', '2', 'user_status', NULL, 'info', 'N', '0', 'admin', '2025-02-07 16:20:31', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (210, 0, '白羊座', '1', 'constellation_type', NULL, 'default', 'N', '0', 'admin', '2025-02-11 14:13:55', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (211, 0, '金牛座', '2', 'constellation_type', NULL, 'default', 'N', '0', 'admin', '2025-02-11 14:14:04', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (212, 0, '双子座', '3', 'constellation_type', NULL, 'default', 'N', '0', 'admin', '2025-02-11 14:14:13', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (213, 0, '巨蟹座', '4', 'constellation_type', NULL, 'default', 'N', '0', 'admin', '2025-02-11 14:14:23', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (214, 0, '狮子座', '5', 'constellation_type', NULL, 'default', 'N', '0', 'admin', '2025-02-11 14:14:33', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (215, 0, '处女座', '6', 'constellation_type', NULL, 'default', 'N', '0', 'admin', '2025-02-11 14:14:42', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (216, 0, '天秤座', '7', 'constellation_type', NULL, 'default', 'N', '0', 'admin', '2025-02-11 14:14:59', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (217, 0, '天蝎座', '8', 'constellation_type', NULL, 'default', 'N', '0', 'admin', '2025-02-11 14:15:16', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (218, 0, '射手座', '9', 'constellation_type', NULL, 'default', 'N', '0', 'admin', '2025-02-11 14:15:24', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (219, 0, '摩羯座', '10', 'constellation_type', NULL, 'default', 'N', '0', 'admin', '2025-02-11 14:15:32', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (220, 0, '水瓶座', '11', 'constellation_type', NULL, 'default', 'N', '0', 'admin', '2025-02-11 14:15:42', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (221, 0, '双鱼座', '12', 'constellation_type', NULL, 'default', 'N', '0', 'admin', '2025-02-11 14:15:49', '', NULL, NULL);

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type`  (
  `dict_id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `dict_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典类型',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_id`) USING BTREE,
  UNIQUE INDEX `dict_type`(`dict_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 105 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '字典类型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dict_type
-- ----------------------------
INSERT INTO `sys_dict_type` VALUES (1, '用户性别', 'sys_user_sex', '0', 'admin', '2024-12-18 14:06:52', '', NULL, '用户性别列表');
INSERT INTO `sys_dict_type` VALUES (2, '菜单状态', 'sys_show_hide', '0', 'admin', '2024-12-18 14:06:52', '', NULL, '菜单状态列表');
INSERT INTO `sys_dict_type` VALUES (3, '系统开关', 'sys_normal_disable', '0', 'admin', '2024-12-18 14:06:52', '', NULL, '系统开关列表');
INSERT INTO `sys_dict_type` VALUES (4, '任务状态', 'sys_job_status', '0', 'admin', '2024-12-18 14:06:52', '', NULL, '任务状态列表');
INSERT INTO `sys_dict_type` VALUES (5, '任务分组', 'sys_job_group', '0', 'admin', '2024-12-18 14:06:52', '', NULL, '任务分组列表');
INSERT INTO `sys_dict_type` VALUES (6, '系统是否', 'sys_yes_no', '0', 'admin', '2024-12-18 14:06:52', '', NULL, '系统是否列表');
INSERT INTO `sys_dict_type` VALUES (7, '通知类型', 'sys_notice_type', '0', 'admin', '2024-12-18 14:06:52', '', NULL, '通知类型列表');
INSERT INTO `sys_dict_type` VALUES (8, '通知状态', 'sys_notice_status', '0', 'admin', '2024-12-18 14:06:53', '', NULL, '通知状态列表');
INSERT INTO `sys_dict_type` VALUES (9, '操作类型', 'sys_oper_type', '0', 'admin', '2024-12-18 14:06:53', '', NULL, '操作类型列表');
INSERT INTO `sys_dict_type` VALUES (10, '系统状态', 'sys_common_status', '0', 'admin', '2024-12-18 14:06:53', '', NULL, '登录状态列表');
INSERT INTO `sys_dict_type` VALUES (100, '案件类型', 'cases_type', '0', 'admin', '2024-12-31 10:58:19', '', NULL, NULL);
INSERT INTO `sys_dict_type` VALUES (101, '案件标签', 'cases_label', '0', 'admin', '2024-12-31 13:34:35', '', NULL, NULL);
INSERT INTO `sys_dict_type` VALUES (102, '系统头像', 'sys_avatar', '0', 'admin', '2025-02-01 21:38:09', 'admin', '2025-02-01 21:44:47', NULL);
INSERT INTO `sys_dict_type` VALUES (103, '用户状态', 'user_status', '0', 'admin', '2025-02-07 16:19:40', '', NULL, NULL);
INSERT INTO `sys_dict_type` VALUES (104, '星座列表', 'constellation_type', '0', 'admin', '2025-02-11 14:12:27', '', NULL, NULL);

-- ----------------------------
-- Table structure for sys_job
-- ----------------------------
DROP TABLE IF EXISTS `sys_job`;
CREATE TABLE `sys_job`  (
  `job_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'DEFAULT' COMMENT '任务组名',
  `invoke_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调用目标字符串',
  `cron_expression` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'cron执行表达式',
  `misfire_policy` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '3' COMMENT '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
  `concurrent` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '是否并发执行（0允许 1禁止）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '状态（0正常 1暂停）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注信息',
  PRIMARY KEY (`job_id`, `job_name`, `job_group`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '定时任务调度表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_job
-- ----------------------------
INSERT INTO `sys_job` VALUES (1, '系统默认（无参）', 'DEFAULT', 'ryTask.ryNoParams', '0/10 * * * * ?', '3', '1', '1', 'admin', '2024-12-18 14:06:58', '', NULL, '');
INSERT INTO `sys_job` VALUES (2, '系统默认（有参）', 'DEFAULT', 'ryTask.ryParams(\'ry\')', '0/15 * * * * ?', '3', '1', '1', 'admin', '2024-12-18 14:06:58', '', NULL, '');
INSERT INTO `sys_job` VALUES (3, '系统默认（多参）', 'DEFAULT', 'ryTask.ryMultipleParams(\'ry\', true, 2000L, 316.50D, 100)', '0/20 * * * * ?', '3', '1', '1', 'admin', '2024-12-18 14:06:58', '', NULL, '');

-- ----------------------------
-- Table structure for sys_job_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_job_log`;
CREATE TABLE `sys_job_log`  (
  `job_log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务日志ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务组名',
  `invoke_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调用目标字符串',
  `job_message` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '日志信息',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '执行状态（0正常 1失败）',
  `exception_info` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '异常信息',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`job_log_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '定时任务调度日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_job_log
-- ----------------------------

-- ----------------------------
-- Table structure for sys_logininfor
-- ----------------------------
DROP TABLE IF EXISTS `sys_logininfor`;
CREATE TABLE `sys_logininfor`  (
  `info_id` bigint NOT NULL AUTO_INCREMENT COMMENT '访问ID',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '用户账号',
  `ipaddr` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '登录IP地址',
  `login_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '登录地点',
  `browser` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '浏览器类型',
  `os` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '操作系统',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
  `msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '提示消息',
  `login_time` datetime NULL DEFAULT NULL COMMENT '访问时间',
  PRIMARY KEY (`info_id`) USING BTREE,
  INDEX `idx_sys_logininfor_lt`(`login_time` ASC) USING BTREE,
  INDEX `idx_sys_logininfor_s`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 150 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统访问记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_logininfor
-- ----------------------------
INSERT INTO `sys_logininfor` VALUES (100, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-18 14:15:33');
INSERT INTO `sys_logininfor` VALUES (101, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-18 14:40:25');
INSERT INTO `sys_logininfor` VALUES (102, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-20 13:26:47');
INSERT INTO `sys_logininfor` VALUES (103, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-20 15:40:21');
INSERT INTO `sys_logininfor` VALUES (104, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-20 21:13:22');
INSERT INTO `sys_logininfor` VALUES (105, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-22 10:25:36');
INSERT INTO `sys_logininfor` VALUES (106, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-22 10:25:41');
INSERT INTO `sys_logininfor` VALUES (107, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-23 16:48:42');
INSERT INTO `sys_logininfor` VALUES (108, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-23 22:28:18');
INSERT INTO `sys_logininfor` VALUES (109, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2024-12-23 22:28:58');
INSERT INTO `sys_logininfor` VALUES (110, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-23 22:29:07');
INSERT INTO `sys_logininfor` VALUES (111, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-23 22:29:16');
INSERT INTO `sys_logininfor` VALUES (112, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2024-12-23 22:30:02');
INSERT INTO `sys_logininfor` VALUES (113, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-23 22:30:08');
INSERT INTO `sys_logininfor` VALUES (114, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-30 14:16:12');
INSERT INTO `sys_logininfor` VALUES (115, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-31 10:56:51');
INSERT INTO `sys_logininfor` VALUES (116, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-31 13:33:55');
INSERT INTO `sys_logininfor` VALUES (117, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-01 00:52:20');
INSERT INTO `sys_logininfor` VALUES (118, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-01 08:28:04');
INSERT INTO `sys_logininfor` VALUES (119, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-03 13:54:13');
INSERT INTO `sys_logininfor` VALUES (120, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-03 15:42:23');
INSERT INTO `sys_logininfor` VALUES (121, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-03 17:50:04');
INSERT INTO `sys_logininfor` VALUES (122, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-03 18:24:48');
INSERT INTO `sys_logininfor` VALUES (123, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-05 12:05:14');
INSERT INTO `sys_logininfor` VALUES (124, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-05 14:29:37');
INSERT INTO `sys_logininfor` VALUES (125, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-08 18:49:57');
INSERT INTO `sys_logininfor` VALUES (126, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-15 10:44:20');
INSERT INTO `sys_logininfor` VALUES (127, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-16 16:30:23');
INSERT INTO `sys_logininfor` VALUES (128, 'admin', '************', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-17 15:12:42');
INSERT INTO `sys_logininfor` VALUES (129, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-19 16:57:27');
INSERT INTO `sys_logininfor` VALUES (130, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-31 20:45:53');
INSERT INTO `sys_logininfor` VALUES (131, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-02-01 21:37:26');
INSERT INTO `sys_logininfor` VALUES (132, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-02-06 14:03:19');
INSERT INTO `sys_logininfor` VALUES (133, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-02-07 16:10:36');
INSERT INTO `sys_logininfor` VALUES (134, 'admin', '************', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-02-08 15:04:06');
INSERT INTO `sys_logininfor` VALUES (135, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-02-10 19:32:09');
INSERT INTO `sys_logininfor` VALUES (136, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-02-11 14:11:46');
INSERT INTO `sys_logininfor` VALUES (137, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-02-11 14:42:07');
INSERT INTO `sys_logininfor` VALUES (138, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2025-02-11 15:26:28');
INSERT INTO `sys_logininfor` VALUES (139, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码已失效', '2025-02-11 15:26:28');
INSERT INTO `sys_logininfor` VALUES (140, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-02-11 15:26:31');
INSERT INTO `sys_logininfor` VALUES (141, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-02-11 22:32:41');
INSERT INTO `sys_logininfor` VALUES (142, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2025-02-15 23:23:09');
INSERT INTO `sys_logininfor` VALUES (143, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-02-15 23:23:13');
INSERT INTO `sys_logininfor` VALUES (144, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-02-16 13:41:43');
INSERT INTO `sys_logininfor` VALUES (145, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-02-16 13:45:43');
INSERT INTO `sys_logininfor` VALUES (146, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-02-16 13:45:57');
INSERT INTO `sys_logininfor` VALUES (147, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码已失效', '2025-02-16 13:45:58');
INSERT INTO `sys_logininfor` VALUES (148, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-02-16 15:09:51');
INSERT INTO `sys_logininfor` VALUES (149, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-02-16 16:51:20');

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `menu_id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '菜单名称',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父菜单ID',
  `order_num` int NULL DEFAULT 0 COMMENT '显示顺序',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '路由参数',
  `route_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '路由名称',
  `is_frame` int NULL DEFAULT 1 COMMENT '是否为外链（0是 1否）',
  `is_cache` int NULL DEFAULT 0 COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
  `perms` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '#' COMMENT '菜单图标',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2013 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '菜单权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES (1, '系统管理', 0, 10, 'system', NULL, '', '', 1, 0, 'M', '0', '0', '', 'system', 'admin', '2024-12-18 14:06:29', 'admin', '2025-02-07 16:35:31', '系统管理目录');
INSERT INTO `sys_menu` VALUES (2, '系统监控', 0, 20, 'monitor', NULL, '', '', 1, 0, 'M', '0', '0', '', 'monitor', 'admin', '2024-12-18 14:06:29', 'admin', '2025-02-07 16:35:43', '系统监控目录');
INSERT INTO `sys_menu` VALUES (3, '系统工具', 0, 30, 'tool', NULL, '', '', 1, 0, 'M', '0', '0', '', 'tool', 'admin', '2024-12-18 14:06:29', 'admin', '2025-02-07 16:35:50', '系统工具目录');
INSERT INTO `sys_menu` VALUES (100, '用户管理', 1, 1, 'user', 'system/user/index', '', '', 1, 0, 'C', '0', '0', 'system:user:list', 'user', 'admin', '2024-12-18 14:06:29', '', NULL, '用户管理菜单');
INSERT INTO `sys_menu` VALUES (101, '角色管理', 1, 2, 'role', 'system/role/index', '', '', 1, 0, 'C', '0', '0', 'system:role:list', 'peoples', 'admin', '2024-12-18 14:06:29', '', NULL, '角色管理菜单');
INSERT INTO `sys_menu` VALUES (102, '菜单管理', 1, 3, 'menu', 'system/menu/index', '', '', 1, 0, 'C', '0', '0', 'system:menu:list', 'tree-table', 'admin', '2024-12-18 14:06:30', '', NULL, '菜单管理菜单');
INSERT INTO `sys_menu` VALUES (103, '部门管理', 1, 4, 'dept', 'system/dept/index', '', '', 1, 0, 'C', '0', '0', 'system:dept:list', 'tree', 'admin', '2024-12-18 14:06:30', '', NULL, '部门管理菜单');
INSERT INTO `sys_menu` VALUES (104, '岗位管理', 1, 5, 'post', 'system/post/index', '', '', 1, 0, 'C', '0', '0', 'system:post:list', 'post', 'admin', '2024-12-18 14:06:30', '', NULL, '岗位管理菜单');
INSERT INTO `sys_menu` VALUES (105, '字典管理', 1, 6, 'dict', 'system/dict/index', '', '', 1, 0, 'C', '0', '0', 'system:dict:list', 'dict', 'admin', '2024-12-18 14:06:30', '', NULL, '字典管理菜单');
INSERT INTO `sys_menu` VALUES (106, '参数设置', 1, 7, 'config', 'system/config/index', '', '', 1, 0, 'C', '0', '0', 'system:config:list', 'edit', 'admin', '2024-12-18 14:06:30', '', NULL, '参数设置菜单');
INSERT INTO `sys_menu` VALUES (107, '通知公告', 1, 8, 'notice', 'system/notice/index', '', '', 1, 0, 'C', '0', '0', 'system:notice:list', 'message', 'admin', '2024-12-18 14:06:30', '', NULL, '通知公告菜单');
INSERT INTO `sys_menu` VALUES (108, '日志管理', 1, 9, 'log', '', '', '', 1, 0, 'M', '0', '0', '', 'log', 'admin', '2024-12-18 14:06:30', '', NULL, '日志管理菜单');
INSERT INTO `sys_menu` VALUES (109, '在线用户', 2, 1, 'online', 'monitor/online/index', '', '', 1, 0, 'C', '0', '0', 'monitor:online:list', 'online', 'admin', '2024-12-18 14:06:31', '', NULL, '在线用户菜单');
INSERT INTO `sys_menu` VALUES (110, '定时任务', 2, 2, 'job', 'monitor/job/index', '', '', 1, 0, 'C', '0', '0', 'monitor:job:list', 'job', 'admin', '2024-12-18 14:06:31', '', NULL, '定时任务菜单');
INSERT INTO `sys_menu` VALUES (111, '数据监控', 2, 3, 'druid', 'monitor/druid/index', '', '', 1, 0, 'C', '0', '0', 'monitor:druid:list', 'druid', 'admin', '2024-12-18 14:06:31', '', NULL, '数据监控菜单');
INSERT INTO `sys_menu` VALUES (112, '服务监控', 2, 4, 'server', 'monitor/server/index', '', '', 1, 0, 'C', '0', '0', 'monitor:server:list', 'server', 'admin', '2024-12-18 14:06:31', '', NULL, '服务监控菜单');
INSERT INTO `sys_menu` VALUES (113, '缓存监控', 2, 5, 'cache', 'monitor/cache/index', '', '', 1, 0, 'C', '0', '0', 'monitor:cache:list', 'redis', 'admin', '2024-12-18 14:06:31', '', NULL, '缓存监控菜单');
INSERT INTO `sys_menu` VALUES (114, '缓存列表', 2, 6, 'cacheList', 'monitor/cache/list', '', '', 1, 0, 'C', '0', '0', 'monitor:cache:list', 'redis-list', 'admin', '2024-12-18 14:06:31', '', NULL, '缓存列表菜单');
INSERT INTO `sys_menu` VALUES (115, '表单构建', 3, 1, 'build', 'tool/build/index', '', '', 1, 0, 'C', '0', '0', 'tool:build:list', 'build', 'admin', '2024-12-18 14:06:31', '', NULL, '表单构建菜单');
INSERT INTO `sys_menu` VALUES (116, '代码生成', 3, 2, 'gen', 'tool/gen/index', '', '', 1, 0, 'C', '0', '0', 'tool:gen:list', 'code', 'admin', '2024-12-18 14:06:31', '', NULL, '代码生成菜单');
INSERT INTO `sys_menu` VALUES (117, '系统接口', 3, 3, 'swagger', 'tool/swagger/index', '', '', 1, 0, 'C', '0', '0', 'tool:swagger:list', 'swagger', 'admin', '2024-12-18 14:06:32', '', NULL, '系统接口菜单');
INSERT INTO `sys_menu` VALUES (500, '操作日志', 108, 1, 'operlog', 'monitor/operlog/index', '', '', 1, 0, 'C', '0', '0', 'monitor:operlog:list', 'form', 'admin', '2024-12-18 14:06:32', '', NULL, '操作日志菜单');
INSERT INTO `sys_menu` VALUES (501, '登录日志', 108, 2, 'logininfor', 'monitor/logininfor/index', '', '', 1, 0, 'C', '0', '0', 'monitor:logininfor:list', 'logininfor', 'admin', '2024-12-18 14:06:32', '', NULL, '登录日志菜单');
INSERT INTO `sys_menu` VALUES (1000, '用户查询', 100, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:query', '#', 'admin', '2024-12-18 14:06:32', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1001, '用户新增', 100, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:add', '#', 'admin', '2024-12-18 14:06:32', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1002, '用户修改', 100, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:edit', '#', 'admin', '2024-12-18 14:06:32', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1003, '用户删除', 100, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:remove', '#', 'admin', '2024-12-18 14:06:32', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1004, '用户导出', 100, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:export', '#', 'admin', '2024-12-18 14:06:32', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1005, '用户导入', 100, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:import', '#', 'admin', '2024-12-18 14:06:32', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1006, '重置密码', 100, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:resetPwd', '#', 'admin', '2024-12-18 14:06:33', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1007, '角色查询', 101, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:query', '#', 'admin', '2024-12-18 14:06:33', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1008, '角色新增', 101, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:add', '#', 'admin', '2024-12-18 14:06:33', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1009, '角色修改', 101, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:edit', '#', 'admin', '2024-12-18 14:06:33', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1010, '角色删除', 101, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:remove', '#', 'admin', '2024-12-18 14:06:33', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1011, '角色导出', 101, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:export', '#', 'admin', '2024-12-18 14:06:33', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1012, '菜单查询', 102, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:query', '#', 'admin', '2024-12-18 14:06:33', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1013, '菜单新增', 102, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:add', '#', 'admin', '2024-12-18 14:06:33', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1014, '菜单修改', 102, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:edit', '#', 'admin', '2024-12-18 14:06:34', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1015, '菜单删除', 102, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:remove', '#', 'admin', '2024-12-18 14:06:34', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1016, '部门查询', 103, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:query', '#', 'admin', '2024-12-18 14:06:34', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1017, '部门新增', 103, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:add', '#', 'admin', '2024-12-18 14:06:34', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1018, '部门修改', 103, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:edit', '#', 'admin', '2024-12-18 14:06:34', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1019, '部门删除', 103, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:remove', '#', 'admin', '2024-12-18 14:06:34', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1020, '岗位查询', 104, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:query', '#', 'admin', '2024-12-18 14:06:34', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1021, '岗位新增', 104, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:add', '#', 'admin', '2024-12-18 14:06:34', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1022, '岗位修改', 104, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:edit', '#', 'admin', '2024-12-18 14:06:35', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1023, '岗位删除', 104, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:remove', '#', 'admin', '2024-12-18 14:06:35', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1024, '岗位导出', 104, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:export', '#', 'admin', '2024-12-18 14:06:35', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1025, '字典查询', 105, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:query', '#', 'admin', '2024-12-18 14:06:35', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1026, '字典新增', 105, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:add', '#', 'admin', '2024-12-18 14:06:35', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1027, '字典修改', 105, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:edit', '#', 'admin', '2024-12-18 14:06:35', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1028, '字典删除', 105, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:remove', '#', 'admin', '2024-12-18 14:06:35', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1029, '字典导出', 105, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:export', '#', 'admin', '2024-12-18 14:06:35', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1030, '参数查询', 106, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:query', '#', 'admin', '2024-12-18 14:06:36', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1031, '参数新增', 106, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:add', '#', 'admin', '2024-12-18 14:06:36', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1032, '参数修改', 106, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:edit', '#', 'admin', '2024-12-18 14:06:36', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1033, '参数删除', 106, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:remove', '#', 'admin', '2024-12-18 14:06:36', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1034, '参数导出', 106, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:export', '#', 'admin', '2024-12-18 14:06:36', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1035, '公告查询', 107, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:query', '#', 'admin', '2024-12-18 14:06:36', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1036, '公告新增', 107, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:add', '#', 'admin', '2024-12-18 14:06:36', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1037, '公告修改', 107, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:edit', '#', 'admin', '2024-12-18 14:06:36', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1038, '公告删除', 107, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:remove', '#', 'admin', '2024-12-18 14:06:36', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1039, '操作查询', 500, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:query', '#', 'admin', '2024-12-18 14:06:37', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1040, '操作删除', 500, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:remove', '#', 'admin', '2024-12-18 14:06:37', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1041, '日志导出', 500, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:export', '#', 'admin', '2024-12-18 14:06:37', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1042, '登录查询', 501, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:query', '#', 'admin', '2024-12-18 14:06:37', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1043, '登录删除', 501, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:remove', '#', 'admin', '2024-12-18 14:06:37', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1044, '日志导出', 501, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:export', '#', 'admin', '2024-12-18 14:06:37', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1045, '账户解锁', 501, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:unlock', '#', 'admin', '2024-12-18 14:06:37', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1046, '在线查询', 109, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:query', '#', 'admin', '2024-12-18 14:06:37', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1047, '批量强退', 109, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:batchLogout', '#', 'admin', '2024-12-18 14:06:38', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1048, '单条强退', 109, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:forceLogout', '#', 'admin', '2024-12-18 14:06:38', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1049, '任务查询', 110, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:query', '#', 'admin', '2024-12-18 14:06:38', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1050, '任务新增', 110, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:add', '#', 'admin', '2024-12-18 14:06:38', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1051, '任务修改', 110, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:edit', '#', 'admin', '2024-12-18 14:06:38', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1052, '任务删除', 110, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:remove', '#', 'admin', '2024-12-18 14:06:38', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1053, '状态修改', 110, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:changeStatus', '#', 'admin', '2024-12-18 14:06:38', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1054, '任务导出', 110, 6, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:export', '#', 'admin', '2024-12-18 14:06:38', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1055, '生成查询', 116, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:query', '#', 'admin', '2024-12-18 14:06:39', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1056, '生成修改', 116, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:edit', '#', 'admin', '2024-12-18 14:06:39', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1057, '生成删除', 116, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:remove', '#', 'admin', '2024-12-18 14:06:39', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1058, '导入代码', 116, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:import', '#', 'admin', '2024-12-18 14:06:39', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1059, '预览代码', 116, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:preview', '#', 'admin', '2024-12-18 14:06:39', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1060, '生成代码', 116, 6, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:code', '#', 'admin', '2024-12-18 14:06:39', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2000, '运营中心', 0, 1, 'operation', NULL, NULL, '', 1, 0, 'M', '0', '0', NULL, 'peoples', 'admin', '2025-02-07 16:26:32', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2001, '用户信息', 2000, 1, 'userInfo', 'operation/userInfo/index', NULL, '', 1, 0, 'C', '0', '0', 'operation:userInfo:list', 'user', 'admin', '2025-02-07 16:48:11', 'admin', '2025-02-10 19:35:24', '用户信息菜单');
INSERT INTO `sys_menu` VALUES (2002, '用户信息查询', 2001, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'operation:userInfo:query', '#', 'admin', '2025-02-07 16:48:12', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2003, '用户信息新增', 2001, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'operation:userInfo:add', '#', 'admin', '2025-02-07 16:48:12', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2004, '用户信息修改', 2001, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'operation:userInfo:edit', '#', 'admin', '2025-02-07 16:48:12', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2005, '用户信息删除', 2001, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'operation:userInfo:remove', '#', 'admin', '2025-02-07 16:48:12', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2006, '用户信息导出', 2001, 5, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'operation:userInfo:export', '#', 'admin', '2025-02-07 16:48:12', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2007, '案件列表', 2000, 1, 'cases', 'operation/cases/index', NULL, '', 1, 0, 'C', '0', '0', 'operation:cases:list', 'list', 'admin', '2025-02-10 20:05:43', 'admin', '2025-02-10 20:13:59', '案件列表菜单');
INSERT INTO `sys_menu` VALUES (2008, '案件列表查询', 2007, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'operation:cases:query', '#', 'admin', '2025-02-10 20:05:43', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2009, '案件列表新增', 2007, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'operation:cases:add', '#', 'admin', '2025-02-10 20:05:43', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2010, '案件列表修改', 2007, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'operation:cases:edit', '#', 'admin', '2025-02-10 20:05:43', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2011, '案件列表删除', 2007, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'operation:cases:remove', '#', 'admin', '2025-02-10 20:05:44', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2012, '案件列表导出', 2007, 5, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'operation:cases:export', '#', 'admin', '2025-02-10 20:05:44', '', NULL, '');

-- ----------------------------
-- Table structure for sys_notice
-- ----------------------------
DROP TABLE IF EXISTS `sys_notice`;
CREATE TABLE `sys_notice`  (
  `notice_id` int NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `notice_title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公告标题',
  `notice_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公告类型（1通知 2公告）',
  `notice_content` longblob NULL COMMENT '公告内容',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`notice_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '通知公告表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_notice
-- ----------------------------
INSERT INTO `sys_notice` VALUES (1, '温馨提醒：2018-07-01 若依新版本发布啦', '2', 0xE696B0E78988E69CACE58685E5AEB9, '0', 'admin', '2024-12-18 14:06:59', '', NULL, '管理员');
INSERT INTO `sys_notice` VALUES (2, '维护通知：2018-07-01 若依系统凌晨维护', '1', 0xE7BBB4E68AA4E58685E5AEB9, '0', 'admin', '2024-12-18 14:06:59', '', NULL, '管理员');

-- ----------------------------
-- Table structure for sys_oper_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_oper_log`;
CREATE TABLE `sys_oper_log`  (
  `oper_id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '模块标题',
  `business_type` int NULL DEFAULT 0 COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  `method` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '方法名称',
  `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '请求方式',
  `operator_type` int NULL DEFAULT 0 COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  `oper_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '操作人员',
  `dept_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '部门名称',
  `oper_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '请求URL',
  `oper_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '主机地址',
  `oper_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '操作地点',
  `oper_param` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '请求参数',
  `json_result` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '返回参数',
  `status` int NULL DEFAULT 0 COMMENT '操作状态（0正常 1异常）',
  `error_msg` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '错误消息',
  `oper_time` datetime NULL DEFAULT NULL COMMENT '操作时间',
  `cost_time` bigint NULL DEFAULT 0 COMMENT '消耗时间',
  PRIMARY KEY (`oper_id`) USING BTREE,
  INDEX `idx_sys_oper_log_bt`(`business_type` ASC) USING BTREE,
  INDEX `idx_sys_oper_log_ot`(`oper_time` ASC) USING BTREE,
  INDEX `idx_sys_oper_log_s`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 356 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '操作日志记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_oper_log
-- ----------------------------
INSERT INTO `sys_oper_log` VALUES (100, '个人信息', 2, 'com.fs.web.controller.system.SysProfileController.updateProfile()', 'PUT', 1, 'admin', '研发部门', '/system/user/profile', '127.0.0.1', '内网IP', '{\"admin\":false,\"email\":\"<EMAIL>\",\"nickName\":\"若依\",\"params\":{},\"phonenumber\":\"***********\",\"sex\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-18 14:47:29', 82);
INSERT INTO `sys_oper_log` VALUES (101, '代码生成', 6, 'com.fs.generator.controller.GenController.importTableSave()', 'POST', 1, 'admin', '研发部门', '/tool/gen/importTable', '127.0.0.1', '内网IP', '{\"tables\":\"sys_notice\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-20 15:40:49', 379);
INSERT INTO `sys_oper_log` VALUES (102, '代码生成', 8, 'com.fs.generator.controller.GenController.batchGenCode()', 'GET', 1, 'admin', '研发部门', '/tool/gen/batchGenCode', '127.0.0.1', '内网IP', '{\"tables\":\"sys_notice\"}', NULL, 0, NULL, '2024-12-20 15:40:53', 196);
INSERT INTO `sys_oper_log` VALUES (103, '用户管理', 2, 'com.fs.admin.controller.system.SysUserControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/user', '127.0.0.1', '内网IP', '{\"admin\":false,\"avatar\":\"\",\"createBy\":\"admin\",\"createTime\":\"2024-12-18 14:06:27\",\"delFlag\":\"0\",\"dept\":{\"ancestors\":\"0,100,101\",\"children\":[],\"deptId\":105,\"deptName\":\"测试部门\",\"leader\":\"若依\",\"orderNum\":3,\"params\":{},\"parentId\":101,\"status\":\"0\"},\"deptId\":105,\"email\":\"<EMAIL>\",\"loginDate\":\"2024-12-18 14:06:27\",\"loginIp\":\"127.0.0.1\",\"nickName\":\"若依\",\"params\":{},\"phonenumber\":\"15666666666\",\"postIds\":[2],\"remark\":\"测试员\",\"roleIds\":[2],\"roles\":[{\"admin\":false,\"dataScope\":\"2\",\"deptCheckStrictly\":false,\"flag\":false,\"menuCheckStrictly\":false,\"params\":{},\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\"}],\"sex\":\"1\",\"status\":\"0\",\"updateBy\":\"admin\",\"userId\":2,\"userName\":\"ry\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-20 21:13:30', 319);
INSERT INTO `sys_oper_log` VALUES (104, '代码生成', 6, 'com.fs.generator.controller.GenControllerAdmin.importTableSave()', 'POST', 1, 'admin', '研发部门', '/tool/gen/importTable', '127.0.0.1', '内网IP', '{\"tables\":\"user_info\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-22 10:26:00', 705);
INSERT INTO `sys_oper_log` VALUES (105, '代码生成', 2, 'com.fs.generator.controller.GenControllerAdmin.editSave()', 'PUT', 1, 'admin', '研发部门', '/tool/gen', '127.0.0.1', '内网IP', '{\"businessName\":\"info\",\"className\":\"UserInfo\",\"columns\":[{\"capJavaField\":\"Id\",\"columnId\":11,\"columnName\":\"id\",\"columnType\":\"int\",\"createBy\":\"admin\",\"createTime\":\"2024-12-22 10:25:59\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":true,\"insert\":true,\"isIncrement\":\"1\",\"isInsert\":\"1\",\"isPk\":\"1\",\"isRequired\":\"0\",\"javaField\":\"id\",\"javaType\":\"Long\",\"list\":false,\"params\":{},\"pk\":true,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":1,\"superColumn\":false,\"tableId\":2,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"Mobile\",\"columnComment\":\"用户手机号码\",\"columnId\":12,\"columnName\":\"mobile\",\"columnType\":\"varchar(20)\",\"createBy\":\"admin\",\"createTime\":\"2024-12-22 10:25:59\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"mobile\",\"javaType\":\"String\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":2,\"superColumn\":false,\"tableId\":2,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"Openid\",\"columnComment\":\"微信登录openid\",\"columnId\":13,\"columnName\":\"openid\",\"columnType\":\"varchar(63)\",\"createBy\":\"admin\",\"createTime\":\"2024-12-22 10:25:59\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"openid\",\"javaType\":\"String\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":3,\"superColumn\":false,\"tableId\":2,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"Password\",\"columnComment\":\"用户密码\",\"columnId\":14,\"columnName\":\"password\",\"columnType\":\"varchar(63)\",\"createBy\":\"admin\",\"createTime\":\"2024-12-22 10:25:59\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"password\",\"javaType\":\"String\",\"l', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-22 10:28:32', 591);
INSERT INTO `sys_oper_log` VALUES (106, '代码生成', 8, 'com.fs.generator.controller.GenControllerAdmin.batchGenCode()', 'GET', 1, 'admin', '研发部门', '/tool/gen/batchGenCode', '127.0.0.1', '内网IP', '{\"tables\":\"user_info\"}', NULL, 0, NULL, '2024-12-22 10:28:37', 338);
INSERT INTO `sys_oper_log` VALUES (107, '用户管理', 2, 'com.fs.swap.admin.controller.system.SysUserControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/user', '127.0.0.1', '内网IP', '{\"admin\":false,\"avatar\":\"\",\"createBy\":\"admin\",\"createTime\":\"2024-12-18 14:06:27\",\"delFlag\":\"0\",\"dept\":{\"ancestors\":\"0,100,101\",\"children\":[],\"deptId\":105,\"deptName\":\"测试部门\",\"leader\":\"若依\",\"orderNum\":3,\"params\":{},\"parentId\":101,\"status\":\"0\"},\"deptId\":105,\"email\":\"<EMAIL>\",\"loginDate\":\"2024-12-18 14:06:27\",\"loginIp\":\"127.0.0.1\",\"nickName\":\"若依\",\"params\":{},\"phonenumber\":\"15666666666\",\"postIds\":[2],\"remark\":\"测试员\",\"roleIds\":[2],\"roles\":[{\"admin\":false,\"dataScope\":\"2\",\"deptCheckStrictly\":false,\"flag\":false,\"menuCheckStrictly\":false,\"params\":{},\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\"}],\"sex\":\"1\",\"status\":\"0\",\"updateBy\":\"admin\",\"userId\":2,\"userName\":\"ry\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-23 16:48:52', 270);
INSERT INTO `sys_oper_log` VALUES (108, '代码生成', 6, 'com.fs.swap.generator.controller.GenControllerAdmin.importTableSave()', 'POST', 1, 'admin', '研发部门', '/tool/gen/importTable', '127.0.0.1', '内网IP', '{\"tables\":\"cases\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-30 14:16:23', 452);
INSERT INTO `sys_oper_log` VALUES (109, '代码生成', 2, 'com.fs.swap.generator.controller.GenControllerAdmin.editSave()', 'PUT', 1, 'admin', '研发部门', '/tool/gen', '127.0.0.1', '内网IP', '{\"businessName\":\"cases\",\"className\":\"Cases\",\"columns\":[{\"capJavaField\":\"Id\",\"columnComment\":\"id\",\"columnId\":27,\"columnName\":\"id\",\"columnType\":\"int\",\"createBy\":\"admin\",\"createTime\":\"2024-12-30 14:16:22\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":true,\"insert\":true,\"isIncrement\":\"1\",\"isInsert\":\"1\",\"isPk\":\"1\",\"isRequired\":\"0\",\"javaField\":\"id\",\"javaType\":\"Long\",\"list\":false,\"params\":{},\"pk\":true,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":1,\"superColumn\":false,\"tableId\":3,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"Title\",\"columnComment\":\"标题\",\"columnId\":28,\"columnName\":\"title\",\"columnType\":\"varchar(255)\",\"createBy\":\"admin\",\"createTime\":\"2024-12-30 14:16:22\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"title\",\"javaType\":\"String\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":2,\"superColumn\":false,\"tableId\":3,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"Description\",\"columnComment\":\"描述\",\"columnId\":29,\"columnName\":\"description\",\"columnType\":\"text\",\"createBy\":\"admin\",\"createTime\":\"2024-12-30 14:16:22\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"textarea\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"description\",\"javaType\":\"String\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":3,\"superColumn\":false,\"tableId\":3,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"Evidence\",\"columnComment\":\"附件\",\"columnId\":30,\"columnName\":\"evidence\",\"columnType\":\"text\",\"createBy\":\"admin\",\"createTime\":\"2024-12-30 14:16:22\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"textarea\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"0\",\"javaField\":\"evidence\",\"javaType\":\"', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-30 14:17:25', 372);
INSERT INTO `sys_oper_log` VALUES (110, '代码生成', 8, 'com.fs.swap.generator.controller.GenControllerAdmin.batchGenCode()', 'GET', 1, 'admin', '研发部门', '/tool/gen/batchGenCode', '127.0.0.1', '内网IP', '{\"tables\":\"cases\"}', NULL, 0, NULL, '2024-12-30 14:17:36', 403);
INSERT INTO `sys_oper_log` VALUES (111, '字典类型', 1, 'com.fs.swap.admin.controller.system.SysDictTypeControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/type', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"dictName\":\"案件类型\",\"dictType\":\"cases_type\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 10:58:19', 69);
INSERT INTO `sys_oper_log` VALUES (112, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"恋爱矛盾\",\"dictSort\":1,\"dictType\":\"cases_type\",\"dictValue\":\"1\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 10:59:25', 67);
INSERT INTO `sys_oper_log` VALUES (113, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"日常琐事\",\"dictSort\":0,\"dictType\":\"cases_type\",\"dictValue\":\"2\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 10:59:47', 57);
INSERT INTO `sys_oper_log` VALUES (114, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"金钱与消费\",\"dictSort\":0,\"dictType\":\"cases_type\",\"dictValue\":\"3\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 10:59:58', 55);
INSERT INTO `sys_oper_log` VALUES (115, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"节日与纪念日\",\"dictSort\":4,\"dictType\":\"cases_type\",\"dictValue\":\"4\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 11:00:22', 59);
INSERT INTO `sys_oper_log` VALUES (116, '字典数据', 2, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2024-12-31 10:59:47\",\"default\":false,\"dictCode\":101,\"dictLabel\":\"日常琐事\",\"dictSort\":2,\"dictType\":\"cases_type\",\"dictValue\":\"2\",\"isDefault\":\"N\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 11:00:44', 58);
INSERT INTO `sys_oper_log` VALUES (117, '字典数据', 2, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2024-12-31 10:59:58\",\"default\":false,\"dictCode\":102,\"dictLabel\":\"金钱与消费\",\"dictSort\":3,\"dictType\":\"cases_type\",\"dictValue\":\"3\",\"isDefault\":\"N\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 11:00:52', 59);
INSERT INTO `sys_oper_log` VALUES (118, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"约会与出行\",\"dictSort\":0,\"dictType\":\"cases_type\",\"dictValue\":\"5\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 11:01:26', 55);
INSERT INTO `sys_oper_log` VALUES (119, '字典数据', 2, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2024-12-31 11:01:26\",\"default\":false,\"dictCode\":104,\"dictLabel\":\"约会与出行\",\"dictSort\":5,\"dictType\":\"cases_type\",\"dictValue\":\"5\",\"isDefault\":\"N\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 11:01:30', 56);
INSERT INTO `sys_oper_log` VALUES (120, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"沟通问题\",\"dictSort\":6,\"dictType\":\"cases_type\",\"dictValue\":\"6\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 11:01:42', 60);
INSERT INTO `sys_oper_log` VALUES (121, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"亲密关系\",\"dictSort\":7,\"dictType\":\"cases_type\",\"dictValue\":\"7\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 11:02:01', 58);
INSERT INTO `sys_oper_log` VALUES (122, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"家庭与朋友关系\",\"dictSort\":8,\"dictType\":\"cases_type\",\"dictValue\":\"8\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 11:02:11', 55);
INSERT INTO `sys_oper_log` VALUES (123, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"工作与生活平衡\",\"dictSort\":9,\"dictType\":\"cases_type\",\"dictValue\":\"9\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 11:02:28', 55);
INSERT INTO `sys_oper_log` VALUES (124, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"分手相关\",\"dictSort\":10,\"dictType\":\"cases_type\",\"dictValue\":\"10\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 11:02:39', 61);
INSERT INTO `sys_oper_log` VALUES (125, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\" 特殊事件\",\"dictSort\":11,\"dictType\":\"cases_type\",\"dictValue\":\"11\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 11:02:53', 54);
INSERT INTO `sys_oper_log` VALUES (126, '字典类型', 1, 'com.fs.swap.admin.controller.system.SysDictTypeControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/type', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"dictName\":\"案件标签\",\"dictType\":\"cases_label\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:34:35', 53);
INSERT INTO `sys_oper_log` VALUES (127, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"忽冷忽热\",\"dictSort\":1,\"dictType\":\"cases_label\",\"dictValue\":\"1\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:35:28', 48);
INSERT INTO `sys_oper_log` VALUES (128, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"太过黏人\",\"dictSort\":2,\"dictType\":\"cases_label\",\"dictValue\":\"2\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:35:37', 47);
INSERT INTO `sys_oper_log` VALUES (129, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"吃醋\",\"dictSort\":3,\"dictType\":\"cases_label\",\"dictValue\":\"3\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:35:46', 46);
INSERT INTO `sys_oper_log` VALUES (130, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"不信任\",\"dictSort\":4,\"dictType\":\"cases_label\",\"dictValue\":\"4\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:35:53', 46);
INSERT INTO `sys_oper_log` VALUES (131, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"太少沟通\",\"dictSort\":5,\"dictType\":\"cases_label\",\"dictValue\":\"5\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:36:03', 45);
INSERT INTO `sys_oper_log` VALUES (132, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"情绪化\",\"dictSort\":5,\"dictType\":\"cases_label\",\"dictValue\":\"5\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:36:12', 49);
INSERT INTO `sys_oper_log` VALUES (133, '字典数据', 2, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2024-12-31 13:36:11\",\"default\":false,\"dictCode\":116,\"dictLabel\":\"情绪化\",\"dictSort\":6,\"dictType\":\"cases_label\",\"dictValue\":\"6\",\"isDefault\":\"N\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:36:19', 46);
INSERT INTO `sys_oper_log` VALUES (134, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"不主动联系\",\"dictSort\":8,\"dictType\":\"cases_label\",\"dictValue\":\"8\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:36:31', 49);
INSERT INTO `sys_oper_log` VALUES (135, '字典数据', 2, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2024-12-31 13:36:31\",\"default\":false,\"dictCode\":117,\"dictLabel\":\"不主动联系\",\"dictSort\":7,\"dictType\":\"cases_label\",\"dictValue\":\"7\",\"isDefault\":\"N\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:36:36', 48);
INSERT INTO `sys_oper_log` VALUES (136, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"家务分配\",\"dictSort\":8,\"dictType\":\"cases_label\",\"dictValue\":\"8\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:37:00', 48);
INSERT INTO `sys_oper_log` VALUES (137, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"吵架\",\"dictSort\":9,\"dictType\":\"cases_label\",\"dictValue\":\"9\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:37:12', 48);
INSERT INTO `sys_oper_log` VALUES (138, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"乱扔东西\",\"dictSort\":10,\"dictType\":\"cases_label\",\"dictValue\":\"10\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:37:22', 48);
INSERT INTO `sys_oper_log` VALUES (139, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"洗碗问题\",\"dictSort\":11,\"dictType\":\"cases_label\",\"dictValue\":\"11\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:37:34', 49);
INSERT INTO `sys_oper_log` VALUES (140, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"清洁习惯\",\"dictSort\":12,\"dictType\":\"cases_label\",\"dictValue\":\"12\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:37:45', 45);
INSERT INTO `sys_oper_log` VALUES (141, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"作息不一致\",\"dictSort\":13,\"dictType\":\"cases_label\",\"dictValue\":\"13\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:37:57', 47);
INSERT INTO `sys_oper_log` VALUES (142, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"借东西不还\",\"dictSort\":14,\"dictType\":\"cases_label\",\"dictValue\":\"14\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:38:05', 49);
INSERT INTO `sys_oper_log` VALUES (143, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"开销分配\",\"dictSort\":15,\"dictType\":\"cases_label\",\"dictValue\":\"15\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:39:58', 60);
INSERT INTO `sys_oper_log` VALUES (144, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"花钱太多\",\"dictSort\":16,\"dictType\":\"cases_label\",\"dictValue\":\"16\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:40:17', 46);
INSERT INTO `sys_oper_log` VALUES (145, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"不愿承担\",\"dictSort\":17,\"dictType\":\"cases_label\",\"dictValue\":\"17\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:40:31', 47);
INSERT INTO `sys_oper_log` VALUES (146, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"礼物预算\",\"dictSort\":18,\"dictType\":\"cases_label\",\"dictValue\":\"18\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:40:42', 48);
INSERT INTO `sys_oper_log` VALUES (147, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"借钱不还\",\"dictSort\":19,\"dictType\":\"cases_label\",\"dictValue\":\"19\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:40:54', 50);
INSERT INTO `sys_oper_log` VALUES (148, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"不存钱\",\"dictSort\":20,\"dictType\":\"cases_label\",\"dictValue\":\"20\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:41:04', 48);
INSERT INTO `sys_oper_log` VALUES (149, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"奢侈消费\",\"dictSort\":21,\"dictType\":\"cases_label\",\"dictValue\":\"21\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:41:14', 51);
INSERT INTO `sys_oper_log` VALUES (150, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"忘记纪念日\",\"dictSort\":0,\"dictType\":\"cases_label\",\"dictValue\":\"22\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:41:27', 46);
INSERT INTO `sys_oper_log` VALUES (151, '字典数据', 2, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2024-12-31 13:41:27\",\"default\":false,\"dictCode\":132,\"dictLabel\":\"忘记纪念日\",\"dictSort\":22,\"dictType\":\"cases_label\",\"dictValue\":\"22\",\"isDefault\":\"N\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:41:34', 48);
INSERT INTO `sys_oper_log` VALUES (152, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"礼物不合适\",\"dictSort\":23,\"dictType\":\"cases_label\",\"dictValue\":\"23\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:41:44', 47);
INSERT INTO `sys_oper_log` VALUES (153, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"计划失败\",\"dictSort\":24,\"dictType\":\"cases_label\",\"dictValue\":\"24\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:41:54', 49);
INSERT INTO `sys_oper_log` VALUES (154, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"没有准备\",\"dictSort\":25,\"dictType\":\"cases_label\",\"dictValue\":\"25\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:42:03', 47);
INSERT INTO `sys_oper_log` VALUES (155, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"节日冷淡\",\"dictSort\":26,\"dictType\":\"cases_label\",\"dictValue\":\"26\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:42:15', 47);
INSERT INTO `sys_oper_log` VALUES (156, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"行程冲突\",\"dictSort\":27,\"dictType\":\"cases_label\",\"dictValue\":\"27\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:42:26', 50);
INSERT INTO `sys_oper_log` VALUES (157, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"没有仪式感\",\"dictSort\":28,\"dictType\":\"cases_label\",\"dictValue\":\"28\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:42:36', 49);
INSERT INTO `sys_oper_log` VALUES (158, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"迟到\",\"dictSort\":29,\"dictType\":\"cases_label\",\"dictValue\":\"29\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:42:51', 45);
INSERT INTO `sys_oper_log` VALUES (159, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"手机上瘾\",\"dictSort\":30,\"dictType\":\"cases_label\",\"dictValue\":\"30\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:43:07', 49);
INSERT INTO `sys_oper_log` VALUES (160, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"目的地选择\",\"dictSort\":31,\"dictType\":\"cases_label\",\"dictValue\":\"31\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:43:17', 46);
INSERT INTO `sys_oper_log` VALUES (161, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"cssClass\":\"\",\"default\":false,\"dictLabel\":\"行程安排\",\"dictSort\":32,\"dictType\":\"cases_label\",\"dictValue\":\"32\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:43:33', 47);
INSERT INTO `sys_oper_log` VALUES (162, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"不守时\",\"dictSort\":33,\"dictType\":\"cases_label\",\"dictValue\":\"33\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:43:47', 45);
INSERT INTO `sys_oper_log` VALUES (163, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"出行分歧\",\"dictSort\":34,\"dictType\":\"cases_label\",\"dictValue\":\"34\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:43:58', 47);
INSERT INTO `sys_oper_log` VALUES (164, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"旅行预算\",\"dictSort\":35,\"dictType\":\"cases_label\",\"dictValue\":\"35\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:44:09', 48);
INSERT INTO `sys_oper_log` VALUES (165, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"冷战\",\"dictSort\":36,\"dictType\":\"cases_label\",\"dictValue\":\"36\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:44:29', 47);
INSERT INTO `sys_oper_log` VALUES (166, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"情绪爆发\",\"dictSort\":37,\"dictType\":\"cases_label\",\"dictValue\":\"37\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:44:44', 46);
INSERT INTO `sys_oper_log` VALUES (167, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"cssClass\":\"\",\"default\":false,\"dictLabel\":\"太少沟通\",\"dictSort\":38,\"dictType\":\"cases_label\",\"dictValue\":\"38\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:45:07', 51);
INSERT INTO `sys_oper_log` VALUES (168, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"太直接\",\"dictSort\":39,\"dictType\":\"cases_label\",\"dictValue\":\"39\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:45:21', 45);
INSERT INTO `sys_oper_log` VALUES (169, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"不回应\",\"dictSort\":40,\"dictType\":\"cases_label\",\"dictValue\":\"40\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:45:31', 47);
INSERT INTO `sys_oper_log` VALUES (170, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"含糊其辞\",\"dictSort\":41,\"dictType\":\"cases_label\",\"dictValue\":\"41\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:45:40', 47);
INSERT INTO `sys_oper_log` VALUES (171, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"逃避问题\",\"dictSort\":42,\"dictType\":\"cases_label\",\"dictValue\":\"42\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:45:51', 46);
INSERT INTO `sys_oper_log` VALUES (172, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"太过黏人\",\"dictSort\":43,\"dictType\":\"cases_label\",\"dictValue\":\"43\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:46:09', 47);
INSERT INTO `sys_oper_log` VALUES (173, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"疏远\",\"dictSort\":44,\"dictType\":\"cases_label\",\"dictValue\":\"44\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:46:25', 46);
INSERT INTO `sys_oper_log` VALUES (174, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"不愿接触\",\"dictSort\":45,\"dictType\":\"cases_label\",\"dictValue\":\"45\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:46:34', 48);
INSERT INTO `sys_oper_log` VALUES (175, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"缺乏关心\",\"dictSort\":46,\"dictType\":\"cases_label\",\"dictValue\":\"46\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:46:44', 44);
INSERT INTO `sys_oper_log` VALUES (176, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"不愿分享\",\"dictSort\":47,\"dictType\":\"cases_label\",\"dictValue\":\"47\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:46:53', 46);
INSERT INTO `sys_oper_log` VALUES (177, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"cssClass\":\"\",\"default\":false,\"dictLabel\":\"不给空间\",\"dictSort\":48,\"dictType\":\"cases_label\",\"dictValue\":\"48\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:47:07', 47);
INSERT INTO `sys_oper_log` VALUES (178, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"cssClass\":\"\",\"default\":false,\"dictLabel\":\"亲密度下降\",\"dictSort\":49,\"dictType\":\"cases_label\",\"dictValue\":\"49\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:47:18', 46);
INSERT INTO `sys_oper_log` VALUES (179, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"父母干涉\",\"dictSort\":50,\"dictType\":\"cases_label\",\"dictValue\":\"50\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:47:42', 45);
INSERT INTO `sys_oper_log` VALUES (180, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"朋友优先\",\"dictSort\":51,\"dictType\":\"cases_label\",\"dictValue\":\"51\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:47:56', 48);
INSERT INTO `sys_oper_log` VALUES (181, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"不愿见家人\",\"dictSort\":52,\"dictType\":\"cases_label\",\"dictValue\":\"52\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:48:06', 51);
INSERT INTO `sys_oper_log` VALUES (182, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"忽略朋友圈\",\"dictSort\":53,\"dictType\":\"cases_label\",\"dictValue\":\"53\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:48:16', 49);
INSERT INTO `sys_oper_log` VALUES (183, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"亲友冲突\",\"dictSort\":54,\"dictType\":\"cases_label\",\"dictValue\":\"54\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:48:24', 46);
INSERT INTO `sys_oper_log` VALUES (184, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"父母不支持\",\"dictSort\":55,\"dictType\":\"cases_label\",\"dictValue\":\"55\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:48:34', 46);
INSERT INTO `sys_oper_log` VALUES (185, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"聚会太频繁\",\"dictSort\":56,\"dictType\":\"cases_label\",\"dictValue\":\"56\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:48:44', 46);
INSERT INTO `sys_oper_log` VALUES (186, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"cssClass\":\"57\",\"default\":false,\"dictLabel\":\"忙于工作\",\"dictSort\":0,\"dictType\":\"cases_label\",\"dictValue\":\"57\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:48:54', 46);
INSERT INTO `sys_oper_log` VALUES (187, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"忽略感情\",\"dictSort\":58,\"dictType\":\"cases_label\",\"dictValue\":\"58\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:49:04', 50);
INSERT INTO `sys_oper_log` VALUES (188, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"cssClass\":\"\",\"default\":false,\"dictLabel\":\"下班打游戏\",\"dictSort\":59,\"dictType\":\"cases_label\",\"dictValue\":\"59\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:49:17', 47);
INSERT INTO `sys_oper_log` VALUES (189, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"压力大\",\"dictSort\":60,\"dictType\":\"cases_label\",\"dictValue\":\"60\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:49:28', 50);
INSERT INTO `sys_oper_log` VALUES (190, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"时间不足\",\"dictSort\":61,\"dictType\":\"cases_label\",\"dictValue\":\"61\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:49:43', 50);
INSERT INTO `sys_oper_log` VALUES (191, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"加班过多\",\"dictSort\":62,\"dictType\":\"cases_label\",\"dictValue\":\"62\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:50:09', 46);
INSERT INTO `sys_oper_log` VALUES (192, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"提出分手\",\"dictSort\":63,\"dictType\":\"cases_label\",\"dictValue\":\"63\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:50:21', 45);
INSERT INTO `sys_oper_log` VALUES (193, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"挽回失败\",\"dictSort\":64,\"dictType\":\"cases_label\",\"dictValue\":\"64\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:50:30', 46);
INSERT INTO `sys_oper_log` VALUES (194, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"不愿分开\",\"dictSort\":65,\"dictType\":\"cases_label\",\"dictValue\":\"65\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:50:38', 49);
INSERT INTO `sys_oper_log` VALUES (195, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"分手后纠缠\",\"dictSort\":66,\"dictType\":\"cases_label\",\"dictValue\":\"66\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:50:48', 47);
INSERT INTO `sys_oper_log` VALUES (196, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"分手理由\",\"dictSort\":67,\"dictType\":\"cases_label\",\"dictValue\":\"67\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:50:59', 49);
INSERT INTO `sys_oper_log` VALUES (197, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"感情淡了\",\"dictSort\":68,\"dictType\":\"cases_label\",\"dictValue\":\"68\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:51:11', 49);
INSERT INTO `sys_oper_log` VALUES (198, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"纠结继续\",\"dictSort\":69,\"dictType\":\"cases_label\",\"dictValue\":\"69\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:51:19', 48);
INSERT INTO `sys_oper_log` VALUES (199, '字典数据', 2, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2024-12-31 13:48:54\",\"cssClass\":\"\",\"default\":false,\"dictCode\":167,\"dictLabel\":\"忙于工作\",\"dictSort\":57,\"dictType\":\"cases_label\",\"dictValue\":\"57\",\"isDefault\":\"N\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:51:33', 46);
INSERT INTO `sys_oper_log` VALUES (200, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"突发情况\",\"dictSort\":70,\"dictType\":\"cases_label\",\"dictValue\":\"70\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:52:28', 46);
INSERT INTO `sys_oper_log` VALUES (201, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"cssClass\":\"\",\"default\":false,\"dictLabel\":\"难以原谅\",\"dictSort\":71,\"dictType\":\"cases_label\",\"dictValue\":\"71\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:52:42', 44);
INSERT INTO `sys_oper_log` VALUES (202, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"短暂争执\",\"dictSort\":72,\"dictType\":\"cases_label\",\"dictValue\":\"72\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:52:56', 48);
INSERT INTO `sys_oper_log` VALUES (203, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"突然变故\",\"dictSort\":73,\"dictType\":\"cases_label\",\"dictValue\":\"73\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:53:06', 49);
INSERT INTO `sys_oper_log` VALUES (204, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"不在状态\",\"dictSort\":74,\"dictType\":\"cases_label\",\"dictValue\":\"74\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:53:18', 45);
INSERT INTO `sys_oper_log` VALUES (205, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"关系紧张\",\"dictSort\":75,\"dictType\":\"cases_label\",\"dictValue\":\"75\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:53:26', 46);
INSERT INTO `sys_oper_log` VALUES (206, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"临时问题\",\"dictSort\":76,\"dictType\":\"cases_label\",\"dictValue\":\"76\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-31 13:53:37', 48);
INSERT INTO `sys_oper_log` VALUES (207, '参数管理', 1, 'com.fs.swap.admin.controller.system.SysConfigControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/config', '127.0.0.1', '内网IP', '{\"configKey\":\"jury.tag.recommend\",\"configName\":\"标签推荐\",\"configType\":\"N\",\"configValue\":\"-\",\"createBy\":\"admin\",\"params\":{},\"remark\":\"根据类型推荐标签\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-01 08:28:51', 87);
INSERT INTO `sys_oper_log` VALUES (208, '参数管理', 2, 'com.fs.swap.admin.controller.system.SysConfigControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/config', '127.0.0.1', '内网IP', '{\"configId\":100,\"configKey\":\"jury.tag.recommend\",\"configName\":\"标签推荐\",\"configType\":\"N\",\"configValue\":\"{ \\\"1\\\": [\\\"1\\\", \\\"2\\\", \\\"3\\\", \\\"4\\\", \\\"5\\\", \\\"6\\\", \\\"7\\\"], \\\"2\\\": [\\\"8\\\", \\\"9\\\", \\\"10\\\", \\\"11\\\", \\\"12\\\", \\\"13\\\", \\\"14\\\"], \\\"3\\\": [\\\"15\\\", \\\"16\\\", \\\"17\\\", \\\"18\\\", \\\"19\\\", \\\"20\\\", \\\"21\\\"], \\\"4\\\": [\\\"22\\\", \\\"23\\\", \\\"24\\\", \\\"25\\\", \\\"26\\\", \\\"27\\\", \\\"28\\\"], \\\"5\\\": [\\\"29\\\", \\\"30\\\", \\\"31\\\", \\\"32\\\", \\\"33\\\", \\\"34\\\", \\\"35\\\"], \\\"6\\\": [\\\"36\\\", \\\"37\\\", \\\"38\\\", \\\"39\\\", \\\"40\\\", \\\"41\\\", \\\"42\\\"], \\\"7\\\": [\\\"43\\\", \\\"44\\\", \\\"45\\\", \\\"46\\\", \\\"47\\\", \\\"48\\\", \\\"49\\\"], \\\"8\\\": [\\\"50\\\", \\\"51\\\", \\\"52\\\", \\\"53\\\", \\\"54\\\", \\\"55\\\", \\\"56\\\"], \\\"9\\\": [\\\"57\\\", \\\"58\\\", \\\"59\\\", \\\"60\\\", \\\"61\\\", \\\"62\\\"], \\\"10\\\": [\\\"63\\\", \\\"64\\\", \\\"65\\\", \\\"66\\\", \\\"67\\\", \\\"68\\\",\\\"69\\\"], \\\"11\\\": [\\\"70\\\", \\\"71\\\", \\\"72\\\", \\\"73\\\", \\\"74\\\", \\\"75\\\", \\\"76\\\"] }\",\"createBy\":\"admin\",\"createTime\":\"2025-01-01 08:28:51\",\"params\":{},\"remark\":\"根据类型推荐标签\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-01 08:41:17', 118);
INSERT INTO `sys_oper_log` VALUES (209, '代码生成', 6, 'com.fs.swap.generator.controller.GenControllerAdmin.importTableSave()', 'POST', 1, 'admin', '研发部门', '/tool/gen/importTable', '127.0.0.1', '内网IP', '{\"tables\":\"review_daily_count,review_paid_package\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-03 13:54:33', 514);
INSERT INTO `sys_oper_log` VALUES (210, '代码生成', 2, 'com.fs.swap.generator.controller.GenControllerAdmin.editSave()', 'PUT', 1, 'admin', '研发部门', '/tool/gen', '127.0.0.1', '内网IP', '{\"businessName\":\"count\",\"className\":\"ReviewDailyCount\",\"columns\":[{\"capJavaField\":\"Id\",\"columnId\":42,\"columnName\":\"id\",\"columnType\":\"bigint\",\"createBy\":\"admin\",\"createTime\":\"2025-01-03 13:54:33\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":true,\"insert\":true,\"isIncrement\":\"1\",\"isInsert\":\"1\",\"isPk\":\"1\",\"isRequired\":\"0\",\"javaField\":\"id\",\"javaType\":\"Long\",\"list\":false,\"params\":{},\"pk\":true,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":1,\"superColumn\":false,\"tableId\":4,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"UserId\",\"columnComment\":\"用户ID\",\"columnId\":43,\"columnName\":\"user_id\",\"columnType\":\"bigint\",\"createBy\":\"admin\",\"createTime\":\"2025-01-03 13:54:33\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"userId\",\"javaType\":\"Long\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":2,\"superColumn\":false,\"tableId\":4,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"FreeCount\",\"columnComment\":\"剩余免费次数\",\"columnId\":44,\"columnName\":\"free_count\",\"columnType\":\"int\",\"createBy\":\"admin\",\"createTime\":\"2025-01-03 13:54:33\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"freeCount\",\"javaType\":\"Long\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":3,\"superColumn\":false,\"tableId\":4,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"CreateTime\",\"columnId\":45,\"columnName\":\"create_time\",\"columnType\":\"datetime\",\"createBy\":\"admin\",\"createTime\":\"2025-01-03 13:54:33\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"datetime\",\"increment\":false,\"insert\":true,\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isPk\":\"0\",\"isRequired\":\"1\",\"javaField\":\"createTime\",\"javaType\":\"Date\",\"list\":false,\"params\":{},\"pk\":false,\"query\":false,\"queryTyp', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-03 13:56:31', 227);
INSERT INTO `sys_oper_log` VALUES (211, '代码生成', 2, 'com.fs.swap.generator.controller.GenControllerAdmin.editSave()', 'PUT', 1, 'admin', '研发部门', '/tool/gen', '127.0.0.1', '内网IP', '{\"businessName\":\"package\",\"className\":\"ReviewPaidPackage\",\"columns\":[{\"capJavaField\":\"Id\",\"columnId\":46,\"columnName\":\"id\",\"columnType\":\"bigint\",\"createBy\":\"admin\",\"createTime\":\"2025-01-03 13:54:33\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":true,\"insert\":true,\"isIncrement\":\"1\",\"isInsert\":\"1\",\"isPk\":\"1\",\"isRequired\":\"0\",\"javaField\":\"id\",\"javaType\":\"Long\",\"list\":false,\"params\":{},\"pk\":true,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":1,\"superColumn\":false,\"tableId\":5,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"UserId\",\"columnComment\":\"用户ID\",\"columnId\":47,\"columnName\":\"user_id\",\"columnType\":\"bigint\",\"createBy\":\"admin\",\"createTime\":\"2025-01-03 13:54:33\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"userId\",\"javaType\":\"Long\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":2,\"superColumn\":false,\"tableId\":5,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"RemainCount\",\"columnComment\":\"剩余次数\",\"columnId\":48,\"columnName\":\"remain_count\",\"columnType\":\"int\",\"createBy\":\"admin\",\"createTime\":\"2025-01-03 13:54:33\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"remainCount\",\"javaType\":\"Long\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":3,\"superColumn\":false,\"tableId\":5,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"ExpireTime\",\"columnComment\":\"过期时间\",\"columnId\":49,\"columnName\":\"expire_time\",\"columnType\":\"datetime\",\"createBy\":\"admin\",\"createTime\":\"2025-01-03 13:54:33\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"datetime\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"expireTime\",\"javaTyp', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-03 13:57:07', 248);
INSERT INTO `sys_oper_log` VALUES (212, '代码生成', 8, 'com.fs.swap.generator.controller.GenControllerAdmin.batchGenCode()', 'GET', 1, 'admin', '研发部门', '/tool/gen/batchGenCode', '127.0.0.1', '内网IP', '{\"tables\":\"review_daily_count\"}', NULL, 0, NULL, '2025-01-03 13:57:10', 335);
INSERT INTO `sys_oper_log` VALUES (213, '代码生成', 8, 'com.fs.swap.generator.controller.GenControllerAdmin.batchGenCode()', 'GET', 1, 'admin', '研发部门', '/tool/gen/batchGenCode', '127.0.0.1', '内网IP', '{\"tables\":\"review_paid_package\"}', NULL, 0, NULL, '2025-01-03 14:06:47', 77);
INSERT INTO `sys_oper_log` VALUES (214, '参数管理', 1, 'com.fs.swap.admin.controller.system.SysConfigControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/config', '127.0.0.1', '内网IP', '{\"configKey\":\"review_cost_silver\",\"configName\":\"购买1次评审所消耗俸银\",\"configType\":\"N\",\"configValue\":\"200\",\"createBy\":\"admin\",\"params\":{}}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-03 15:44:08', 80);
INSERT INTO `sys_oper_log` VALUES (215, '参数管理', 2, 'com.fs.swap.admin.controller.system.SysConfigControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/config', '127.0.0.1', '内网IP', '{\"configId\":100,\"configKey\":\"jury.tag.recommend\",\"configName\":\"标签推荐\",\"configType\":\"Y\",\"configValue\":\"{ \\\"1\\\": [\\\"1\\\", \\\"2\\\", \\\"3\\\", \\\"4\\\", \\\"5\\\", \\\"6\\\", \\\"7\\\"], \\\"2\\\": [\\\"8\\\", \\\"9\\\", \\\"10\\\", \\\"11\\\", \\\"12\\\", \\\"13\\\", \\\"14\\\"], \\\"3\\\": [\\\"15\\\", \\\"16\\\", \\\"17\\\", \\\"18\\\", \\\"19\\\", \\\"20\\\", \\\"21\\\"], \\\"4\\\": [\\\"22\\\", \\\"23\\\", \\\"24\\\", \\\"25\\\", \\\"26\\\", \\\"27\\\", \\\"28\\\"], \\\"5\\\": [\\\"29\\\", \\\"30\\\", \\\"31\\\", \\\"32\\\", \\\"33\\\", \\\"34\\\", \\\"35\\\"], \\\"6\\\": [\\\"36\\\", \\\"37\\\", \\\"38\\\", \\\"39\\\", \\\"40\\\", \\\"41\\\", \\\"42\\\"], \\\"7\\\": [\\\"43\\\", \\\"44\\\", \\\"45\\\", \\\"46\\\", \\\"47\\\", \\\"48\\\", \\\"49\\\"], \\\"8\\\": [\\\"50\\\", \\\"51\\\", \\\"52\\\", \\\"53\\\", \\\"54\\\", \\\"55\\\", \\\"56\\\"], \\\"9\\\": [\\\"57\\\", \\\"58\\\", \\\"59\\\", \\\"60\\\", \\\"61\\\", \\\"62\\\"], \\\"10\\\": [\\\"63\\\", \\\"64\\\", \\\"65\\\", \\\"66\\\", \\\"67\\\", \\\"68\\\",\\\"69\\\"], \\\"11\\\": [\\\"70\\\", \\\"71\\\", \\\"72\\\", \\\"73\\\", \\\"74\\\", \\\"75\\\", \\\"76\\\"] }\",\"createBy\":\"admin\",\"createTime\":\"2025-01-01 08:28:51\",\"params\":{},\"remark\":\"根据类型推荐标签\",\"updateBy\":\"admin\",\"updateTime\":\"2025-01-01 08:41:17\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-03 15:45:50', 72);
INSERT INTO `sys_oper_log` VALUES (216, '参数管理', 2, 'com.fs.swap.admin.controller.system.SysConfigControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/config', '127.0.0.1', '内网IP', '{\"configId\":101,\"configKey\":\"review_cost_silver\",\"configName\":\"购买1次评审所消耗俸银\",\"configType\":\"Y\",\"configValue\":\"200\",\"createBy\":\"admin\",\"createTime\":\"2025-01-03 15:44:08\",\"params\":{},\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-03 15:45:54', 73);
INSERT INTO `sys_oper_log` VALUES (217, '参数管理', 3, 'com.fs.swap.admin.controller.system.SysConfigControllerAdmin.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/config/101', '127.0.0.1', '内网IP', '[101]', NULL, 1, '内置参数【review_cost_silver】不能删除 ', '2025-01-03 15:45:58', 20);
INSERT INTO `sys_oper_log` VALUES (218, '参数管理', 2, 'com.fs.swap.admin.controller.system.SysConfigControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/config', '127.0.0.1', '内网IP', '{\"configId\":101,\"configKey\":\"jury.review.silver\",\"configName\":\"购买1次评审所消耗俸银\",\"configType\":\"Y\",\"configValue\":\"200\",\"createBy\":\"admin\",\"createTime\":\"2025-01-03 15:44:08\",\"params\":{},\"updateBy\":\"admin\",\"updateTime\":\"2025-01-03 15:45:53\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-03 15:53:54', 84);
INSERT INTO `sys_oper_log` VALUES (219, '代码生成', 6, 'com.fs.swap.generator.controller.GenControllerAdmin.importTableSave()', 'POST', 1, 'admin', '研发部门', '/tool/gen/importTable', '127.0.0.1', '内网IP', '{\"tables\":\"user_votes\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-03 17:50:24', 323);
INSERT INTO `sys_oper_log` VALUES (220, '代码生成', 2, 'com.fs.swap.generator.controller.GenControllerAdmin.synchDb()', 'GET', 1, 'admin', '研发部门', '/tool/gen/synchDb/user_votes', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-03 17:52:13', 275);
INSERT INTO `sys_oper_log` VALUES (221, '代码生成', 2, 'com.fs.swap.generator.controller.GenControllerAdmin.synchDb()', 'GET', 1, 'admin', '研发部门', '/tool/gen/synchDb/user_votes', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-03 17:52:35', 225);
INSERT INTO `sys_oper_log` VALUES (222, '代码生成', 2, 'com.fs.swap.generator.controller.GenControllerAdmin.editSave()', 'PUT', 1, 'admin', '研发部门', '/tool/gen', '127.0.0.1', '内网IP', '{\"businessName\":\"votes\",\"className\":\"UserVotes\",\"columns\":[{\"capJavaField\":\"Id\",\"columnComment\":\"id\",\"columnId\":51,\"columnName\":\"id\",\"columnType\":\"bigint\",\"createBy\":\"admin\",\"createTime\":\"2025-01-03 17:50:24\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":true,\"insert\":true,\"isIncrement\":\"1\",\"isInsert\":\"1\",\"isPk\":\"1\",\"isRequired\":\"0\",\"javaField\":\"id\",\"javaType\":\"Long\",\"list\":false,\"params\":{},\"pk\":true,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":1,\"superColumn\":false,\"tableId\":6,\"updateBy\":\"\",\"updateTime\":\"2025-01-03 17:52:35\",\"usableColumn\":false},{\"capJavaField\":\"UserId\",\"columnComment\":\"投票用户ID\",\"columnId\":52,\"columnName\":\"user_id\",\"columnType\":\"bigint\",\"createBy\":\"admin\",\"createTime\":\"2025-01-03 17:50:24\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"userId\",\"javaType\":\"Long\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":2,\"superColumn\":false,\"tableId\":6,\"updateBy\":\"\",\"updateTime\":\"2025-01-03 17:52:35\",\"usableColumn\":false},{\"capJavaField\":\"CasesId\",\"columnComment\":\"案件ID\",\"columnId\":53,\"columnName\":\"cases_id\",\"columnType\":\"bigint\",\"createBy\":\"admin\",\"createTime\":\"2025-01-03 17:50:24\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"casesId\",\"javaType\":\"Long\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":3,\"superColumn\":false,\"tableId\":6,\"updateBy\":\"\",\"updateTime\":\"2025-01-03 17:52:35\",\"usableColumn\":false},{\"capJavaField\":\"VoteType\",\"columnComment\":\"投票类型\",\"columnId\":54,\"columnName\":\"vote_type\",\"columnType\":\"tinyint\",\"createBy\":\"admin\",\"createTime\":\"2025-01-03 17:50:24\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"select\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-03 17:53:45', 279);
INSERT INTO `sys_oper_log` VALUES (223, '代码生成', 8, 'com.fs.swap.generator.controller.GenControllerAdmin.batchGenCode()', 'GET', 1, 'admin', '研发部门', '/tool/gen/batchGenCode', '127.0.0.1', '内网IP', '{\"tables\":\"user_votes\"}', NULL, 0, NULL, '2025-01-03 17:53:50', 215);
INSERT INTO `sys_oper_log` VALUES (224, '代码生成', 6, 'com.fs.swap.generator.controller.GenControllerAdmin.importTableSave()', 'POST', 1, 'admin', '研发部门', '/tool/gen/importTable', '127.0.0.1', '内网IP', '{\"tables\":\"cases_comment_likes,cases_comments\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-03 18:25:04', 469);
INSERT INTO `sys_oper_log` VALUES (225, '代码生成', 2, 'com.fs.swap.generator.controller.GenControllerAdmin.editSave()', 'PUT', 1, 'admin', '研发部门', '/tool/gen', '127.0.0.1', '内网IP', '{\"businessName\":\"likes\",\"className\":\"CasesCommentLikes\",\"columns\":[{\"capJavaField\":\"CommentId\",\"columnComment\":\"评论ID\",\"columnId\":57,\"columnName\":\"comment_id\",\"columnType\":\"bigint\",\"createBy\":\"admin\",\"createTime\":\"2025-01-03 18:25:03\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isPk\":\"1\",\"isRequired\":\"0\",\"javaField\":\"commentId\",\"javaType\":\"Long\",\"list\":false,\"params\":{},\"pk\":true,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":1,\"superColumn\":false,\"tableId\":7,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"UserId\",\"columnComment\":\"点赞用户ID\",\"columnId\":58,\"columnName\":\"user_id\",\"columnType\":\"bigint\",\"createBy\":\"admin\",\"createTime\":\"2025-01-03 18:25:03\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isPk\":\"1\",\"isRequired\":\"0\",\"javaField\":\"userId\",\"javaType\":\"Long\",\"list\":false,\"params\":{},\"pk\":true,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":2,\"superColumn\":false,\"tableId\":7,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"Status\",\"columnComment\":\"状态：1-已点赞 2-已取消\",\"columnId\":59,\"columnName\":\"status\",\"columnType\":\"tinyint(1)\",\"createBy\":\"admin\",\"createTime\":\"2025-01-03 18:25:03\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"radio\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"status\",\"javaType\":\"Integer\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":3,\"superColumn\":false,\"tableId\":7,\"updateBy\":\"\",\"usableColumn\":false}],\"crud\":true,\"functionAuthor\":\"zy\",\"functionName\":\"评论点赞\",\"genPath\":\"/\",\"genType\":\"0\",\"moduleName\":\"system\",\"options\":\"{}\",\"packageName\":\"com.fs.swap.system\",\"params\":{},\"sub\":false,\"tableComment\":\"评论点赞表\",\"tableId\":7,\"tableName\":\"cases_comment_likes\",\"tplCategory\":\"crud\",\"tplWebType\":\"element-ui\",\"tree\":false}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-03 18:25:29', 124);
INSERT INTO `sys_oper_log` VALUES (226, '代码生成', 2, 'com.fs.swap.generator.controller.GenControllerAdmin.editSave()', 'PUT', 1, 'admin', '研发部门', '/tool/gen', '127.0.0.1', '内网IP', '{\"businessName\":\"comments\",\"className\":\"CasesComments\",\"columns\":[{\"capJavaField\":\"Id\",\"columnId\":60,\"columnName\":\"id\",\"columnType\":\"bigint\",\"createBy\":\"admin\",\"createTime\":\"2025-01-03 18:25:03\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":true,\"insert\":true,\"isIncrement\":\"1\",\"isInsert\":\"1\",\"isPk\":\"1\",\"isRequired\":\"0\",\"javaField\":\"id\",\"javaType\":\"Long\",\"list\":false,\"params\":{},\"pk\":true,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":1,\"superColumn\":false,\"tableId\":8,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"CasesId\",\"columnComment\":\"案件ID\",\"columnId\":61,\"columnName\":\"cases_id\",\"columnType\":\"bigint\",\"createBy\":\"admin\",\"createTime\":\"2025-01-03 18:25:03\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"casesId\",\"javaType\":\"Long\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":2,\"superColumn\":false,\"tableId\":8,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"UserId\",\"columnComment\":\"评论用户ID\",\"columnId\":62,\"columnName\":\"user_id\",\"columnType\":\"bigint\",\"createBy\":\"admin\",\"createTime\":\"2025-01-03 18:25:03\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"userId\",\"javaType\":\"Long\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":3,\"superColumn\":false,\"tableId\":8,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"Content\",\"columnComment\":\"评论内容\",\"columnId\":63,\"columnName\":\"content\",\"columnType\":\"text\",\"createBy\":\"admin\",\"createTime\":\"2025-01-03 18:25:03\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"editor\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"content\",\"javaType\":\"String\",\"list\":true,\"p', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-03 18:25:46', 368);
INSERT INTO `sys_oper_log` VALUES (227, '代码生成', 8, 'com.fs.swap.generator.controller.GenControllerAdmin.batchGenCode()', 'GET', 1, 'admin', '研发部门', '/tool/gen/batchGenCode', '127.0.0.1', '内网IP', '{\"tables\":\"cases_comment_likes\"}', NULL, 0, NULL, '2025-01-03 18:25:49', 147);
INSERT INTO `sys_oper_log` VALUES (228, '代码生成', 8, 'com.fs.swap.generator.controller.GenControllerAdmin.batchGenCode()', 'GET', 1, 'admin', '研发部门', '/tool/gen/batchGenCode', '127.0.0.1', '内网IP', '{\"tables\":\"cases_comments\"}', NULL, 0, NULL, '2025-01-03 18:27:33', 93);
INSERT INTO `sys_oper_log` VALUES (229, '代码生成', 8, 'com.fs.swap.generator.controller.GenControllerAdmin.batchGenCode()', 'GET', 1, 'admin', '研发部门', '/tool/gen/batchGenCode', '127.0.0.1', '内网IP', '{\"tables\":\"cases_comments\"}', NULL, 0, NULL, '2025-01-05 12:05:40', 320);
INSERT INTO `sys_oper_log` VALUES (230, '代码生成', 6, 'com.fs.swap.generator.controller.GenControllerAdmin.importTableSave()', 'POST', 1, 'admin', '研发部门', '/tool/gen/importTable', '127.0.0.1', '内网IP', '{\"tables\":\"cases_reply\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-08 18:50:27', 270);
INSERT INTO `sys_oper_log` VALUES (231, '代码生成', 2, 'com.fs.swap.generator.controller.GenControllerAdmin.editSave()', 'PUT', 1, 'admin', '研发部门', '/tool/gen', '127.0.0.1', '内网IP', '{\"businessName\":\"reply\",\"className\":\"CasesReply\",\"columns\":[{\"capJavaField\":\"Id\",\"columnComment\":\"id\",\"columnId\":70,\"columnName\":\"id\",\"columnType\":\"int\",\"createBy\":\"admin\",\"createTime\":\"2025-01-08 18:50:26\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":true,\"insert\":true,\"isIncrement\":\"1\",\"isInsert\":\"1\",\"isPk\":\"1\",\"isRequired\":\"0\",\"javaField\":\"id\",\"javaType\":\"Long\",\"list\":false,\"params\":{},\"pk\":true,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":1,\"superColumn\":false,\"tableId\":9,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"CasesId\",\"columnComment\":\"cases_id\",\"columnId\":71,\"columnName\":\"cases_id\",\"columnType\":\"int\",\"createBy\":\"admin\",\"createTime\":\"2025-01-08 18:50:26\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"casesId\",\"javaType\":\"Long\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":2,\"superColumn\":false,\"tableId\":9,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"UserId\",\"columnComment\":\"user_id\",\"columnId\":72,\"columnName\":\"user_id\",\"columnType\":\"int\",\"createBy\":\"admin\",\"createTime\":\"2025-01-08 18:50:26\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"userId\",\"javaType\":\"Long\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":3,\"superColumn\":false,\"tableId\":9,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"Description\",\"columnComment\":\"描述\",\"columnId\":73,\"columnName\":\"description\",\"columnType\":\"text\",\"createBy\":\"admin\",\"createTime\":\"2025-01-08 18:50:27\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"textarea\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"description\",\"javaType\":', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-08 18:51:29', 264);
INSERT INTO `sys_oper_log` VALUES (232, '代码生成', 8, 'com.fs.swap.generator.controller.GenControllerAdmin.batchGenCode()', 'GET', 1, 'admin', '研发部门', '/tool/gen/batchGenCode', '127.0.0.1', '内网IP', '{\"tables\":\"cases_reply\"}', NULL, 0, NULL, '2025-01-08 18:51:33', 195);
INSERT INTO `sys_oper_log` VALUES (233, '代码生成', 6, 'com.fs.swap.generator.controller.GenControllerAdmin.importTableSave()', 'POST', 1, 'admin', '研发部门', '/tool/gen/importTable', '127.0.0.1', '内网IP', '{\"tables\":\"user_login_record\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-15 10:45:37', 330);
INSERT INTO `sys_oper_log` VALUES (234, '代码生成', 2, 'com.fs.swap.generator.controller.GenControllerAdmin.editSave()', 'PUT', 1, 'admin', '研发部门', '/tool/gen', '127.0.0.1', '内网IP', '{\"businessName\":\"loginRecord\",\"className\":\"UserLoginRecord\",\"columns\":[{\"capJavaField\":\"Id\",\"columnComment\":\"id\",\"columnId\":77,\"columnName\":\"id\",\"columnType\":\"int\",\"createBy\":\"admin\",\"createTime\":\"2025-01-15 10:45:37\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":true,\"insert\":true,\"isIncrement\":\"1\",\"isInsert\":\"1\",\"isPk\":\"1\",\"isRequired\":\"0\",\"javaField\":\"id\",\"javaType\":\"Long\",\"list\":false,\"params\":{},\"pk\":true,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":1,\"superColumn\":false,\"tableId\":10,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"UserId\",\"columnId\":78,\"columnName\":\"user_id\",\"columnType\":\"mediumtext\",\"createBy\":\"admin\",\"createTime\":\"2025-01-15 10:45:37\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"textarea\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"userId\",\"javaType\":\"String\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":2,\"superColumn\":false,\"tableId\":10,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"Ip\",\"columnId\":79,\"columnName\":\"ip\",\"columnType\":\"varchar(128)\",\"createBy\":\"admin\",\"createTime\":\"2025-01-15 10:45:37\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"ip\",\"javaType\":\"String\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":3,\"superColumn\":false,\"tableId\":10,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"Location\",\"columnId\":80,\"columnName\":\"location\",\"columnType\":\"varchar(255)\",\"createBy\":\"admin\",\"createTime\":\"2025-01-15 10:45:37\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"0\",\"javaField\":\"location\",\"javaType\":\"String\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-15 10:47:49', 252);
INSERT INTO `sys_oper_log` VALUES (235, '代码生成', 8, 'com.fs.swap.generator.controller.GenControllerAdmin.batchGenCode()', 'GET', 1, 'admin', '研发部门', '/tool/gen/batchGenCode', '127.0.0.1', '内网IP', '{\"tables\":\"user_login_record\"}', NULL, 0, NULL, '2025-01-15 10:47:53', 298);
INSERT INTO `sys_oper_log` VALUES (236, '代码生成', 6, 'com.fs.swap.generator.controller.GenControllerAdmin.importTableSave()', 'POST', 1, 'admin', '研发部门', '/tool/gen/importTable', '127.0.0.1', '内网IP', '{\"tables\":\"user_invite_record\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-16 16:41:03', 262);
INSERT INTO `sys_oper_log` VALUES (237, '代码生成', 2, 'com.fs.swap.generator.controller.GenControllerAdmin.editSave()', 'PUT', 1, 'admin', '研发部门', '/tool/gen', '127.0.0.1', '内网IP', '{\"businessName\":\"inviteRecord\",\"className\":\"UserInviteRecord\",\"columns\":[{\"capJavaField\":\"Id\",\"columnComment\":\"id\",\"columnId\":82,\"columnName\":\"id\",\"columnType\":\"int\",\"createBy\":\"admin\",\"createTime\":\"2025-01-16 16:41:03\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":true,\"insert\":true,\"isIncrement\":\"1\",\"isInsert\":\"1\",\"isPk\":\"1\",\"isRequired\":\"0\",\"javaField\":\"id\",\"javaType\":\"Long\",\"list\":false,\"params\":{},\"pk\":true,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":1,\"superColumn\":false,\"tableId\":11,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"InviterUserId\",\"columnComment\":\"邀请人id\",\"columnId\":83,\"columnName\":\"inviter_user_id\",\"columnType\":\"int\",\"createBy\":\"admin\",\"createTime\":\"2025-01-16 16:41:03\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"inviterUserId\",\"javaType\":\"Long\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":2,\"superColumn\":false,\"tableId\":11,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"InviteeUserId\",\"columnComment\":\"被邀请人id\",\"columnId\":84,\"columnName\":\"invitee_user_id\",\"columnType\":\"int\",\"createBy\":\"admin\",\"createTime\":\"2025-01-16 16:41:03\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"inviteeUserId\",\"javaType\":\"Long\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":3,\"superColumn\":false,\"tableId\":11,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"CreateTime\",\"columnComment\":\"创建时间\",\"columnId\":85,\"columnName\":\"create_time\",\"columnType\":\"datetime\",\"createBy\":\"admin\",\"createTime\":\"2025-01-16 16:41:03\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"datetime\",\"increment\":false,\"insert\":true,\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isPk\":\"0\",\"isRequired\":\"1\",\"javaField\":\"creat', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-16 16:42:07', 222);
INSERT INTO `sys_oper_log` VALUES (238, '代码生成', 8, 'com.fs.swap.generator.controller.GenControllerAdmin.batchGenCode()', 'GET', 1, 'admin', '研发部门', '/tool/gen/batchGenCode', '127.0.0.1', '内网IP', '{\"tables\":\"user_invite_record\"}', NULL, 0, NULL, '2025-01-16 16:42:10', 235);
INSERT INTO `sys_oper_log` VALUES (239, '代码生成', 6, 'com.fs.swap.generator.controller.GenControllerAdmin.importTableSave()', 'POST', 1, 'admin', '研发部门', '/tool/gen/importTable', '************', '内网IP', '{\"tables\":\"user_silver_record\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-17 15:12:57', 320);
INSERT INTO `sys_oper_log` VALUES (240, '代码生成', 2, 'com.fs.swap.generator.controller.GenControllerAdmin.editSave()', 'PUT', 1, 'admin', '研发部门', '/tool/gen', '************', '内网IP', '{\"businessName\":\"record\",\"className\":\"UserSilverRecord\",\"columns\":[{\"capJavaField\":\"Id\",\"columnComment\":\"id\",\"columnId\":86,\"columnName\":\"id\",\"columnType\":\"int\",\"createBy\":\"admin\",\"createTime\":\"2025-01-17 15:12:56\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isPk\":\"1\",\"isRequired\":\"0\",\"javaField\":\"id\",\"javaType\":\"Long\",\"list\":false,\"params\":{},\"pk\":true,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":1,\"superColumn\":false,\"tableId\":12,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"UserId\",\"columnComment\":\"user_id\",\"columnId\":87,\"columnName\":\"user_id\",\"columnType\":\"int\",\"createBy\":\"admin\",\"createTime\":\"2025-01-17 15:12:56\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"userId\",\"javaType\":\"Long\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":2,\"superColumn\":false,\"tableId\":12,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"Event\",\"columnComment\":\"事件\",\"columnId\":88,\"columnName\":\"event\",\"columnType\":\"varchar(10)\",\"createBy\":\"admin\",\"createTime\":\"2025-01-17 15:12:56\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"event\",\"javaType\":\"String\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":3,\"superColumn\":false,\"tableId\":12,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"Type\",\"columnComment\":\"类型\",\"columnId\":89,\"columnName\":\"type\",\"columnType\":\"char(1)\",\"createBy\":\"admin\",\"createTime\":\"2025-01-17 15:12:56\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"select\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"type\",\"javaType\":\"String\",\"li', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-17 15:13:31', 246);
INSERT INTO `sys_oper_log` VALUES (241, '代码生成', 8, 'com.fs.swap.generator.controller.GenControllerAdmin.batchGenCode()', 'GET', 1, 'admin', '研发部门', '/tool/gen/batchGenCode', '************', '内网IP', '{\"tables\":\"user_silver_record\"}', NULL, 0, NULL, '2025-01-17 15:13:36', 212);
INSERT INTO `sys_oper_log` VALUES (242, '参数管理', 1, 'com.fs.swap.admin.controller.system.SysConfigControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/config', '127.0.0.1', '内网IP', '{\"configKey\":\"jury.everyday.publish.count\",\"configName\":\"用户每日可发布案件数\",\"configType\":\"Y\",\"configValue\":\"2\",\"createBy\":\"admin\",\"params\":{}}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-19 16:58:40', 114);
INSERT INTO `sys_oper_log` VALUES (243, '参数管理', 2, 'com.fs.swap.admin.controller.system.SysConfigControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/config', '127.0.0.1', '内网IP', '{\"configId\":101,\"configKey\":\"jury.review.silver\",\"configName\":\"购买1次评审所消耗俸银\",\"configType\":\"Y\",\"configValue\":\"50\",\"createBy\":\"admin\",\"createTime\":\"2025-01-03 15:44:08\",\"params\":{},\"updateBy\":\"admin\",\"updateTime\":\"2025-01-03 15:53:54\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-31 20:46:22', 103);
INSERT INTO `sys_oper_log` VALUES (244, '字典类型', 1, 'com.fs.swap.admin.controller.system.SysDictTypeControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/type', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"dictName\":\"系统头像\",\"dictType\":\"system_avatar\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-01 21:38:09', 60);
INSERT INTO `sys_oper_log` VALUES (245, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"1\",\"dictSort\":1,\"dictType\":\"system_avatar\",\"dictValue\":\"airplane\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-01 21:39:07', 61);
INSERT INTO `sys_oper_log` VALUES (246, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"2\",\"dictSort\":0,\"dictType\":\"system_avatar\",\"dictValue\":\"apple\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-01 21:39:20', 69);
INSERT INTO `sys_oper_log` VALUES (247, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"3\",\"dictSort\":3,\"dictType\":\"system_avatar\",\"dictValue\":\"book\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-01 21:39:35', 68);
INSERT INTO `sys_oper_log` VALUES (248, '字典数据', 2, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-02-01 21:39:20\",\"default\":false,\"dictCode\":188,\"dictLabel\":\"2\",\"dictSort\":2,\"dictType\":\"system_avatar\",\"dictValue\":\"apple\",\"isDefault\":\"N\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-01 21:39:42', 73);
INSERT INTO `sys_oper_log` VALUES (249, '字典数据', 2, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-02-01 21:39:07\",\"default\":false,\"dictCode\":187,\"dictLabel\":\"airplane\",\"dictSort\":1,\"dictType\":\"system_avatar\",\"dictValue\":\"airplane\",\"isDefault\":\"N\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-01 21:40:07', 66);
INSERT INTO `sys_oper_log` VALUES (250, '字典数据', 2, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-02-01 21:39:20\",\"default\":false,\"dictCode\":188,\"dictLabel\":\"apple\",\"dictSort\":2,\"dictType\":\"system_avatar\",\"dictValue\":\"apple\",\"isDefault\":\"N\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-01 21:40:13', 66);
INSERT INTO `sys_oper_log` VALUES (251, '字典数据', 2, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-02-01 21:39:34\",\"default\":false,\"dictCode\":189,\"dictLabel\":\"book\",\"dictSort\":3,\"dictType\":\"system_avatar\",\"dictValue\":\"book\",\"isDefault\":\"N\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-01 21:40:18', 57);
INSERT INTO `sys_oper_log` VALUES (252, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"broccoli\",\"dictSort\":4,\"dictType\":\"system_avatar\",\"dictValue\":\"broccoli\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-01 21:40:31', 70);
INSERT INTO `sys_oper_log` VALUES (253, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"cssClass\":\"5\",\"default\":false,\"dictLabel\":\"camera\",\"dictSort\":0,\"dictType\":\"system_avatar\",\"dictValue\":\"camera\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-01 21:40:41', 72);
INSERT INTO `sys_oper_log` VALUES (254, '字典数据', 2, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-02-01 21:40:41\",\"cssClass\":\"\",\"default\":false,\"dictCode\":191,\"dictLabel\":\"camera\",\"dictSort\":5,\"dictType\":\"system_avatar\",\"dictValue\":\"camera\",\"isDefault\":\"N\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-01 21:40:56', 61);
INSERT INTO `sys_oper_log` VALUES (255, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"cat\",\"dictSort\":6,\"dictType\":\"system_avatar\",\"dictValue\":\"cat\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-01 21:41:08', 64);
INSERT INTO `sys_oper_log` VALUES (256, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"chili\",\"dictSort\":7,\"dictType\":\"system_avatar\",\"dictValue\":\"chili\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-01 21:41:18', 62);
INSERT INTO `sys_oper_log` VALUES (257, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"drum\",\"dictSort\":8,\"dictType\":\"system_avatar\",\"dictValue\":\"drum\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-01 21:41:28', 65);
INSERT INTO `sys_oper_log` VALUES (258, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"duck\",\"dictSort\":9,\"dictType\":\"system_avatar\",\"dictValue\":\"duck\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-01 21:41:41', 66);
INSERT INTO `sys_oper_log` VALUES (259, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"hat\",\"dictSort\":10,\"dictType\":\"system_avatar\",\"dictValue\":\"hat\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-01 21:41:51', 66);
INSERT INTO `sys_oper_log` VALUES (260, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"cssClass\":\"11\",\"default\":false,\"dictLabel\":\"headphones\",\"dictSort\":0,\"dictType\":\"system_avatar\",\"dictValue\":\"headphones\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-01 21:41:59', 75);
INSERT INTO `sys_oper_log` VALUES (261, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"letter\",\"dictSort\":12,\"dictType\":\"system_avatar\",\"dictValue\":\"letter\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-01 21:42:11', 67);
INSERT INTO `sys_oper_log` VALUES (262, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"cssClass\":\"\",\"default\":false,\"dictLabel\":\"mushroom\",\"dictSort\":13,\"dictType\":\"system_avatar\",\"dictValue\":\"mushroom\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-01 21:42:22', 65);
INSERT INTO `sys_oper_log` VALUES (263, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"pizza\",\"dictSort\":14,\"dictType\":\"system_avatar\",\"dictValue\":\"pizza\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-01 21:42:32', 56);
INSERT INTO `sys_oper_log` VALUES (264, '字典数据', 2, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-02-01 21:41:59\",\"cssClass\":\"\",\"default\":false,\"dictCode\":197,\"dictLabel\":\"headphones\",\"dictSort\":11,\"dictType\":\"system_avatar\",\"dictValue\":\"headphones\",\"isDefault\":\"N\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-01 21:42:41', 66);
INSERT INTO `sys_oper_log` VALUES (265, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"saturn\",\"dictSort\":15,\"dictType\":\"system_avatar\",\"dictValue\":\"saturn\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-01 21:42:55', 60);
INSERT INTO `sys_oper_log` VALUES (266, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"shoes\",\"dictSort\":16,\"dictType\":\"system_avatar\",\"dictValue\":\"shoes\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-01 21:43:07', 62);
INSERT INTO `sys_oper_log` VALUES (267, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"sweater\",\"dictSort\":17,\"dictType\":\"system_avatar\",\"dictValue\":\"sweater\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-01 21:43:18', 64);
INSERT INTO `sys_oper_log` VALUES (268, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"tent\",\"dictSort\":18,\"dictType\":\"system_avatar\",\"dictValue\":\"tent\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-01 21:43:29', 62);
INSERT INTO `sys_oper_log` VALUES (269, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"wangzai\",\"dictSort\":19,\"dictType\":\"system_avatar\",\"dictValue\":\"wangzai\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-01 21:43:38', 62);
INSERT INTO `sys_oper_log` VALUES (270, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"watermelon\",\"dictSort\":20,\"dictType\":\"system_avatar\",\"dictValue\":\"watermelon\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-01 21:43:48', 65);
INSERT INTO `sys_oper_log` VALUES (271, '字典类型', 2, 'com.fs.swap.admin.controller.system.SysDictTypeControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/dict/type', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-02-01 21:38:09\",\"dictId\":102,\"dictName\":\"系统头像\",\"dictType\":\"sys_avatar\",\"params\":{},\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-01 21:44:47', 138);
INSERT INTO `sys_oper_log` VALUES (272, '参数管理', 2, 'com.fs.swap.admin.controller.system.SysConfigControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/config', '127.0.0.1', '内网IP', '{\"configId\":102,\"configKey\":\"jury.everyday.publish.count\",\"configName\":\"用户每日可发布案件数\",\"configType\":\"Y\",\"configValue\":\"5\",\"createBy\":\"admin\",\"createTime\":\"2025-01-19 16:58:40\",\"params\":{},\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-06 14:03:48', 123);
INSERT INTO `sys_oper_log` VALUES (273, '字典类型', 1, 'com.fs.swap.admin.controller.system.SysDictTypeControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/type', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"dictName\":\"用户状态\",\"dictType\":\"user_status\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:19:40', 77);
INSERT INTO `sys_oper_log` VALUES (274, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"正常\",\"dictSort\":0,\"dictType\":\"user_status\",\"dictValue\":\"0\",\"listClass\":\"success\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:20:00', 68);
INSERT INTO `sys_oper_log` VALUES (275, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"禁用\",\"dictSort\":1,\"dictType\":\"user_status\",\"dictValue\":\"1\",\"listClass\":\"danger\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:20:16', 74);
INSERT INTO `sys_oper_log` VALUES (276, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"注销\",\"dictSort\":2,\"dictType\":\"user_status\",\"dictValue\":\"2\",\"listClass\":\"info\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:20:31', 69);
INSERT INTO `sys_oper_log` VALUES (277, '代码生成', 2, 'com.fs.swap.generator.controller.GenControllerAdmin.editSave()', 'PUT', 1, 'admin', '研发部门', '/tool/gen', '127.0.0.1', '内网IP', '{\"businessName\":\"info\",\"className\":\"UserInfo\",\"columns\":[{\"capJavaField\":\"Id\",\"columnId\":11,\"columnName\":\"id\",\"columnType\":\"int\",\"createBy\":\"admin\",\"createTime\":\"2024-12-22 10:25:59\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":true,\"insert\":false,\"isIncrement\":\"1\",\"isInsert\":\"0\",\"isList\":\"1\",\"isPk\":\"1\",\"isQuery\":\"1\",\"isRequired\":\"0\",\"javaField\":\"id\",\"javaType\":\"Long\",\"list\":true,\"params\":{},\"pk\":true,\"query\":true,\"queryType\":\"EQ\",\"required\":false,\"sort\":1,\"superColumn\":false,\"tableId\":2,\"updateBy\":\"\",\"updateTime\":\"2024-12-22 10:28:31\",\"usableColumn\":false},{\"capJavaField\":\"Mobile\",\"columnComment\":\"用户手机号码\",\"columnId\":12,\"columnName\":\"mobile\",\"columnType\":\"varchar(20)\",\"createBy\":\"admin\",\"createTime\":\"2024-12-22 10:25:59\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":false,\"insert\":false,\"isEdit\":\"0\",\"isIncrement\":\"0\",\"isInsert\":\"0\",\"isList\":\"0\",\"isPk\":\"0\",\"isQuery\":\"0\",\"isRequired\":\"0\",\"javaField\":\"mobile\",\"javaType\":\"String\",\"list\":false,\"params\":{},\"pk\":false,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":2,\"superColumn\":false,\"tableId\":2,\"updateBy\":\"\",\"updateTime\":\"2024-12-22 10:28:31\",\"usableColumn\":false},{\"capJavaField\":\"Openid\",\"columnComment\":\"微信登录openid\",\"columnId\":13,\"columnName\":\"openid\",\"columnType\":\"varchar(63)\",\"createBy\":\"admin\",\"createTime\":\"2024-12-22 10:25:59\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":false,\"insert\":false,\"isEdit\":\"0\",\"isIncrement\":\"0\",\"isInsert\":\"0\",\"isList\":\"0\",\"isPk\":\"0\",\"isQuery\":\"0\",\"isRequired\":\"0\",\"javaField\":\"openid\",\"javaType\":\"String\",\"list\":false,\"params\":{},\"pk\":false,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":3,\"superColumn\":false,\"tableId\":2,\"updateBy\":\"\",\"updateTime\":\"2024-12-22 10:28:31\",\"usableColumn\":false},{\"capJavaField\":\"Password\",\"columnComment\":\"用户密码\",\"columnId\":14,\"columnName\":\"password\",\"columnType\":\"varchar(63)\",\"createBy\":\"admin\",\"createTime\":\"2024-12-22 10:25:59\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":false,\"insert\":false,\"is', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:21:21', 670);
INSERT INTO `sys_oper_log` VALUES (278, '代码生成', 2, 'com.fs.swap.generator.controller.GenControllerAdmin.editSave()', 'PUT', 1, 'admin', '研发部门', '/tool/gen', '127.0.0.1', '内网IP', '{\"businessName\":\"info\",\"className\":\"UserInfo\",\"columns\":[{\"capJavaField\":\"Id\",\"columnId\":11,\"columnName\":\"id\",\"columnType\":\"int\",\"createBy\":\"admin\",\"createTime\":\"2024-12-22 10:25:59\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":true,\"insert\":false,\"isIncrement\":\"1\",\"isInsert\":\"0\",\"isList\":\"1\",\"isPk\":\"1\",\"isQuery\":\"1\",\"isRequired\":\"0\",\"javaField\":\"id\",\"javaType\":\"Long\",\"list\":true,\"params\":{},\"pk\":true,\"query\":true,\"queryType\":\"EQ\",\"required\":false,\"sort\":1,\"superColumn\":false,\"tableId\":2,\"updateBy\":\"\",\"updateTime\":\"2025-02-07 16:21:20\",\"usableColumn\":false},{\"capJavaField\":\"Mobile\",\"columnComment\":\"用户手机号码\",\"columnId\":12,\"columnName\":\"mobile\",\"columnType\":\"varchar(20)\",\"createBy\":\"admin\",\"createTime\":\"2024-12-22 10:25:59\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":false,\"insert\":false,\"isEdit\":\"0\",\"isIncrement\":\"0\",\"isInsert\":\"0\",\"isList\":\"0\",\"isPk\":\"0\",\"isQuery\":\"0\",\"isRequired\":\"0\",\"javaField\":\"mobile\",\"javaType\":\"String\",\"list\":false,\"params\":{},\"pk\":false,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":2,\"superColumn\":false,\"tableId\":2,\"updateBy\":\"\",\"updateTime\":\"2025-02-07 16:21:20\",\"usableColumn\":false},{\"capJavaField\":\"Openid\",\"columnComment\":\"微信登录openid\",\"columnId\":13,\"columnName\":\"openid\",\"columnType\":\"varchar(63)\",\"createBy\":\"admin\",\"createTime\":\"2024-12-22 10:25:59\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":false,\"insert\":false,\"isEdit\":\"0\",\"isIncrement\":\"0\",\"isInsert\":\"0\",\"isList\":\"0\",\"isPk\":\"0\",\"isQuery\":\"0\",\"isRequired\":\"0\",\"javaField\":\"openid\",\"javaType\":\"String\",\"list\":false,\"params\":{},\"pk\":false,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":3,\"superColumn\":false,\"tableId\":2,\"updateBy\":\"\",\"updateTime\":\"2025-02-07 16:21:20\",\"usableColumn\":false},{\"capJavaField\":\"Password\",\"columnComment\":\"用户密码\",\"columnId\":14,\"columnName\":\"password\",\"columnType\":\"varchar(63)\",\"createBy\":\"admin\",\"createTime\":\"2024-12-22 10:25:59\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":false,\"insert\":false,\"is', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:22:08', 639);
INSERT INTO `sys_oper_log` VALUES (279, '代码生成', 8, 'com.fs.swap.generator.controller.GenControllerAdmin.batchGenCode()', 'GET', 1, 'admin', '研发部门', '/tool/gen/batchGenCode', '127.0.0.1', '内网IP', '{\"tables\":\"user_info\"}', NULL, 0, NULL, '2025-02-07 16:22:15', 192);
INSERT INTO `sys_oper_log` VALUES (280, '菜单管理', 3, 'com.fs.swap.admin.controller.system.SysMenuControllerAdmin.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/4', '127.0.0.1', '内网IP', '4', '{\"msg\":\"菜单已分配,不允许删除\",\"code\":601}', 0, NULL, '2025-02-07 16:23:13', 35);
INSERT INTO `sys_oper_log` VALUES (281, '菜单管理', 1, 'com.fs.swap.admin.controller.system.SysMenuControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createBy\":\"admin\",\"icon\":\"peoples\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"运营中心\",\"menuType\":\"M\",\"orderNum\":1,\"params\":{},\"parentId\":0,\"path\":\"operation\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:26:33', 69);
INSERT INTO `sys_oper_log` VALUES (282, '部门管理', 3, 'com.fs.swap.admin.controller.system.SysDeptControllerAdmin.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/dept/109', '127.0.0.1', '内网IP', '109', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:27:15', 76);
INSERT INTO `sys_oper_log` VALUES (283, '部门管理', 3, 'com.fs.swap.admin.controller.system.SysDeptControllerAdmin.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/dept/108', '127.0.0.1', '内网IP', '108', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:27:18', 71);
INSERT INTO `sys_oper_log` VALUES (284, '部门管理', 3, 'com.fs.swap.admin.controller.system.SysDeptControllerAdmin.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/dept/102', '127.0.0.1', '内网IP', '102', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:27:20', 74);
INSERT INTO `sys_oper_log` VALUES (285, '部门管理', 3, 'com.fs.swap.admin.controller.system.SysDeptControllerAdmin.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/dept/107', '127.0.0.1', '内网IP', '107', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:27:22', 75);
INSERT INTO `sys_oper_log` VALUES (286, '部门管理', 3, 'com.fs.swap.admin.controller.system.SysDeptControllerAdmin.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/dept/106', '127.0.0.1', '内网IP', '106', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:27:24', 73);
INSERT INTO `sys_oper_log` VALUES (287, '部门管理', 3, 'com.fs.swap.admin.controller.system.SysDeptControllerAdmin.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/dept/105', '127.0.0.1', '内网IP', '105', '{\"msg\":\"部门存在用户,不允许删除\",\"code\":601}', 0, NULL, '2025-02-07 16:27:27', 38);
INSERT INTO `sys_oper_log` VALUES (288, '部门管理', 3, 'com.fs.swap.admin.controller.system.SysDeptControllerAdmin.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/dept/104', '127.0.0.1', '内网IP', '104', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:27:31', 70);
INSERT INTO `sys_oper_log` VALUES (289, '部门管理', 3, 'com.fs.swap.admin.controller.system.SysDeptControllerAdmin.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/dept/103', '127.0.0.1', '内网IP', '103', '{\"msg\":\"部门存在用户,不允许删除\",\"code\":601}', 0, NULL, '2025-02-07 16:27:34', 39);
INSERT INTO `sys_oper_log` VALUES (290, '角色管理', 2, 'com.fs.swap.admin.controller.system.SysRoleControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2024-12-18 14:06:28\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":false,\"menuIds\":[],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:28:14', 162);
INSERT INTO `sys_oper_log` VALUES (291, '角色管理', 2, 'com.fs.swap.admin.controller.system.SysRoleControllerAdmin.changeStatus()', 'PUT', 1, 'admin', '研发部门', '/system/role/changeStatus', '127.0.0.1', '内网IP', '{\"admin\":false,\"deptCheckStrictly\":false,\"flag\":false,\"menuCheckStrictly\":false,\"params\":{},\"roleId\":2,\"status\":\"1\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:28:18', 34);
INSERT INTO `sys_oper_log` VALUES (292, '角色管理', 3, 'com.fs.swap.admin.controller.system.SysRoleControllerAdmin.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/role/2', '127.0.0.1', '内网IP', '[2]', NULL, 1, '普通角色已分配,不能删除', '2025-02-07 16:28:20', 87);
INSERT INTO `sys_oper_log` VALUES (293, '岗位管理', 2, 'com.fs.swap.admin.controller.system.SysPostControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/post', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2024-12-18 14:06:28\",\"flag\":false,\"params\":{},\"postCode\":\"ceo\",\"postId\":1,\"postName\":\"ceo\",\"postSort\":1,\"remark\":\"\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:29:31', 73);
INSERT INTO `sys_oper_log` VALUES (294, '角色管理', 4, 'com.fs.swap.admin.controller.system.SysRoleControllerAdmin.cancelAuthUser()', 'PUT', 1, 'admin', '研发部门', '/system/role/authUser/cancel', '127.0.0.1', '内网IP', '{\"roleId\":2,\"userId\":2}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:31:34', 33);
INSERT INTO `sys_oper_log` VALUES (295, '角色管理', 2, 'com.fs.swap.admin.controller.system.SysRoleControllerAdmin.dataScope()', 'PUT', 1, 'admin', '研发部门', '/system/role/dataScope', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2024-12-18 14:06:28\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":false,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":false,\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:31:49', 108);
INSERT INTO `sys_oper_log` VALUES (296, '角色管理', 3, 'com.fs.swap.admin.controller.system.SysRoleControllerAdmin.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/role/2', '127.0.0.1', '内网IP', '[2]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:31:52', 174);
INSERT INTO `sys_oper_log` VALUES (297, '部门管理', 2, 'com.fs.swap.admin.controller.system.SysDeptControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/dept', '127.0.0.1', '内网IP', '{\"ancestors\":\"0\",\"children\":[],\"deptId\":100,\"deptName\":\"卡皮巴拉\",\"email\":\"<EMAIL>\",\"leader\":\"周野\",\"orderNum\":0,\"params\":{},\"parentId\":0,\"phone\":\"17326180826\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:32:29', 83);
INSERT INTO `sys_oper_log` VALUES (298, '部门管理', 3, 'com.fs.swap.admin.controller.system.SysDeptControllerAdmin.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/dept/105', '127.0.0.1', '内网IP', '105', '{\"msg\":\"部门存在用户,不允许删除\",\"code\":601}', 0, NULL, '2025-02-07 16:32:38', 35);
INSERT INTO `sys_oper_log` VALUES (299, '部门管理', 3, 'com.fs.swap.admin.controller.system.SysDeptControllerAdmin.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/dept/101', '127.0.0.1', '内网IP', '101', '{\"msg\":\"存在下级部门,不允许删除\",\"code\":601}', 0, NULL, '2025-02-07 16:32:59', 17);
INSERT INTO `sys_oper_log` VALUES (300, '用户管理', 2, 'com.fs.swap.admin.controller.system.SysUserControllerAdmin.changeStatus()', 'PUT', 1, 'admin', '研发部门', '/system/user/changeStatus', '127.0.0.1', '内网IP', '{\"admin\":true,\"params\":{},\"status\":\"1\",\"userId\":1}', NULL, 1, '不允许操作超级管理员用户', '2025-02-07 16:33:14', 0);
INSERT INTO `sys_oper_log` VALUES (301, '部门管理', 3, 'com.fs.swap.admin.controller.system.SysDeptControllerAdmin.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/dept/105', '127.0.0.1', '内网IP', '105', '{\"msg\":\"部门存在用户,不允许删除\",\"code\":601}', 0, NULL, '2025-02-07 16:33:27', 34);
INSERT INTO `sys_oper_log` VALUES (302, '部门管理', 2, 'com.fs.swap.admin.controller.system.SysDeptControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/dept', '127.0.0.1', '内网IP', '{\"ancestors\":\"0,100\",\"children\":[],\"deptId\":103,\"deptName\":\"研发部门\",\"email\":\"<EMAIL>\",\"leader\":\"周野\",\"orderNum\":1,\"params\":{},\"parentId\":100,\"parentName\":\"深圳总公司\",\"phone\":\"17326180826\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:33:58', 128);
INSERT INTO `sys_oper_log` VALUES (303, '部门管理', 2, 'com.fs.swap.admin.controller.system.SysDeptControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/dept', '127.0.0.1', '内网IP', '{\"ancestors\":\"0,100\",\"children\":[],\"deptId\":105,\"deptName\":\"运营部门\",\"email\":\"<EMAIL>\",\"leader\":\"周野\",\"orderNum\":2,\"params\":{},\"parentId\":100,\"parentName\":\"深圳总公司\",\"phone\":\"17326180826\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:34:52', 129);
INSERT INTO `sys_oper_log` VALUES (304, '部门管理', 3, 'com.fs.swap.admin.controller.system.SysDeptControllerAdmin.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/dept/101', '127.0.0.1', '内网IP', '101', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:34:55', 68);
INSERT INTO `sys_oper_log` VALUES (305, '菜单管理', 3, 'com.fs.swap.admin.controller.system.SysMenuControllerAdmin.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/4', '127.0.0.1', '内网IP', '4', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:35:18', 66);
INSERT INTO `sys_oper_log` VALUES (306, '菜单管理', 2, 'com.fs.swap.admin.controller.system.SysMenuControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createTime\":\"2024-12-18 14:06:29\",\"icon\":\"system\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1,\"menuName\":\"系统管理\",\"menuType\":\"M\",\"orderNum\":10,\"params\":{},\"parentId\":0,\"path\":\"system\",\"perms\":\"\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:35:31', 51);
INSERT INTO `sys_oper_log` VALUES (307, '菜单管理', 2, 'com.fs.swap.admin.controller.system.SysMenuControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createTime\":\"2024-12-18 14:06:29\",\"icon\":\"monitor\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2,\"menuName\":\"系统监控\",\"menuType\":\"M\",\"orderNum\":20,\"params\":{},\"parentId\":0,\"path\":\"monitor\",\"perms\":\"\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:35:43', 49);
INSERT INTO `sys_oper_log` VALUES (308, '菜单管理', 2, 'com.fs.swap.admin.controller.system.SysMenuControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createTime\":\"2024-12-18 14:06:29\",\"icon\":\"tool\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":3,\"menuName\":\"系统工具\",\"menuType\":\"M\",\"orderNum\":30,\"params\":{},\"parentId\":0,\"path\":\"tool\",\"perms\":\"\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:35:50', 48);
INSERT INTO `sys_oper_log` VALUES (309, '用户管理', 3, 'com.fs.swap.admin.controller.system.SysUserControllerAdmin.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/user/2', '127.0.0.1', '内网IP', '[2]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:36:40', 139);
INSERT INTO `sys_oper_log` VALUES (310, '用户管理', 1, 'com.fs.swap.admin.controller.system.SysUserControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/user', '127.0.0.1', '内网IP', '{\"admin\":false,\"createBy\":\"admin\",\"deptId\":100,\"email\":\"<EMAIL>\",\"nickName\":\"周野\",\"params\":{},\"phonenumber\":\"17326180826\",\"postIds\":[1],\"roleIds\":[],\"sex\":\"0\",\"status\":\"0\",\"userId\":100,\"userName\":\"zhouye\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:38:01', 220);
INSERT INTO `sys_oper_log` VALUES (311, '角色管理', 1, 'com.fs.swap.admin.controller.system.SysRoleControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createBy\":\"admin\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[],\"params\":{},\"roleId\":100,\"roleKey\":\"dev\",\"roleName\":\"研发\",\"roleSort\":1,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:38:46', 112);
INSERT INTO `sys_oper_log` VALUES (312, '角色管理', 1, 'com.fs.swap.admin.controller.system.SysRoleControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createBy\":\"admin\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[],\"params\":{},\"roleId\":101,\"roleKey\":\"operation\",\"roleName\":\"运营\",\"roleSort\":1,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:39:08', 108);
INSERT INTO `sys_oper_log` VALUES (313, '用户管理', 4, 'com.fs.swap.admin.controller.system.SysUserControllerAdmin.insertAuthRole()', 'PUT', 1, 'admin', '研发部门', '/system/user/authRole', '127.0.0.1', '内网IP', '{\"roleIds\":\"100\",\"userId\":\"100\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:40:48', 107);
INSERT INTO `sys_oper_log` VALUES (314, '代码生成', 2, 'com.fs.swap.generator.controller.GenControllerAdmin.editSave()', 'PUT', 1, 'admin', '研发部门', '/tool/gen', '127.0.0.1', '内网IP', '{\"businessName\":\"userInfo\",\"className\":\"UserInfo\",\"columns\":[{\"capJavaField\":\"Id\",\"columnId\":11,\"columnName\":\"id\",\"columnType\":\"int\",\"createBy\":\"admin\",\"createTime\":\"2024-12-22 10:25:59\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":true,\"insert\":false,\"isIncrement\":\"1\",\"isInsert\":\"0\",\"isList\":\"1\",\"isPk\":\"1\",\"isQuery\":\"1\",\"isRequired\":\"0\",\"javaField\":\"id\",\"javaType\":\"Long\",\"list\":true,\"params\":{},\"pk\":true,\"query\":true,\"queryType\":\"EQ\",\"required\":false,\"sort\":1,\"superColumn\":false,\"tableId\":2,\"updateBy\":\"\",\"updateTime\":\"2025-02-07 16:22:07\",\"usableColumn\":false},{\"capJavaField\":\"Mobile\",\"columnComment\":\"用户手机号码\",\"columnId\":12,\"columnName\":\"mobile\",\"columnType\":\"varchar(20)\",\"createBy\":\"admin\",\"createTime\":\"2024-12-22 10:25:59\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":false,\"insert\":false,\"isEdit\":\"0\",\"isIncrement\":\"0\",\"isInsert\":\"0\",\"isList\":\"0\",\"isPk\":\"0\",\"isQuery\":\"0\",\"isRequired\":\"0\",\"javaField\":\"mobile\",\"javaType\":\"String\",\"list\":false,\"params\":{},\"pk\":false,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":2,\"superColumn\":false,\"tableId\":2,\"updateBy\":\"\",\"updateTime\":\"2025-02-07 16:22:07\",\"usableColumn\":false},{\"capJavaField\":\"Openid\",\"columnComment\":\"微信登录openid\",\"columnId\":13,\"columnName\":\"openid\",\"columnType\":\"varchar(63)\",\"createBy\":\"admin\",\"createTime\":\"2024-12-22 10:25:59\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":false,\"insert\":false,\"isEdit\":\"0\",\"isIncrement\":\"0\",\"isInsert\":\"0\",\"isList\":\"0\",\"isPk\":\"0\",\"isQuery\":\"0\",\"isRequired\":\"0\",\"javaField\":\"openid\",\"javaType\":\"String\",\"list\":false,\"params\":{},\"pk\":false,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":3,\"superColumn\":false,\"tableId\":2,\"updateBy\":\"\",\"updateTime\":\"2025-02-07 16:22:07\",\"usableColumn\":false},{\"capJavaField\":\"Password\",\"columnComment\":\"用户密码\",\"columnId\":14,\"columnName\":\"password\",\"columnType\":\"varchar(63)\",\"createBy\":\"admin\",\"createTime\":\"2024-12-22 10:25:59\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":false,\"insert\":false', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 16:42:37', 653);
INSERT INTO `sys_oper_log` VALUES (315, '代码生成', 8, 'com.fs.swap.generator.controller.GenControllerAdmin.batchGenCode()', 'GET', 1, 'admin', '研发部门', '/tool/gen/batchGenCode', '127.0.0.1', '内网IP', '{\"tables\":\"user_info\"}', NULL, 0, NULL, '2025-02-07 16:42:41', 76);
INSERT INTO `sys_oper_log` VALUES (316, '代码生成', 2, 'com.fs.swap.generator.controller.GenControllerAdmin.editSave()', 'PUT', 1, 'admin', '研发部门', '/tool/gen', '127.0.0.1', '内网IP', '{\"businessName\":\"userInfo\",\"className\":\"UserInfo\",\"columns\":[{\"capJavaField\":\"Id\",\"columnId\":11,\"columnName\":\"id\",\"columnType\":\"int\",\"createBy\":\"admin\",\"createTime\":\"2024-12-22 10:25:59\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":true,\"insert\":false,\"isIncrement\":\"1\",\"isInsert\":\"0\",\"isList\":\"1\",\"isPk\":\"1\",\"isQuery\":\"1\",\"isRequired\":\"0\",\"javaField\":\"id\",\"javaType\":\"Long\",\"list\":true,\"params\":{},\"pk\":true,\"query\":true,\"queryType\":\"EQ\",\"required\":false,\"sort\":1,\"superColumn\":false,\"tableId\":2,\"updateBy\":\"\",\"updateTime\":\"2025-02-07 16:42:36\",\"usableColumn\":false},{\"capJavaField\":\"Mobile\",\"columnComment\":\"用户手机号码\",\"columnId\":12,\"columnName\":\"mobile\",\"columnType\":\"varchar(20)\",\"createBy\":\"admin\",\"createTime\":\"2024-12-22 10:25:59\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":false,\"insert\":false,\"isEdit\":\"0\",\"isIncrement\":\"0\",\"isInsert\":\"0\",\"isList\":\"0\",\"isPk\":\"0\",\"isQuery\":\"0\",\"isRequired\":\"0\",\"javaField\":\"mobile\",\"javaType\":\"String\",\"list\":false,\"params\":{},\"pk\":false,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":2,\"superColumn\":false,\"tableId\":2,\"updateBy\":\"\",\"updateTime\":\"2025-02-07 16:42:36\",\"usableColumn\":false},{\"capJavaField\":\"Openid\",\"columnComment\":\"微信登录openid\",\"columnId\":13,\"columnName\":\"openid\",\"columnType\":\"varchar(63)\",\"createBy\":\"admin\",\"createTime\":\"2024-12-22 10:25:59\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":false,\"insert\":false,\"isEdit\":\"0\",\"isIncrement\":\"0\",\"isInsert\":\"0\",\"isList\":\"0\",\"isPk\":\"0\",\"isQuery\":\"0\",\"isRequired\":\"0\",\"javaField\":\"openid\",\"javaType\":\"String\",\"list\":false,\"params\":{},\"pk\":false,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":3,\"superColumn\":false,\"tableId\":2,\"updateBy\":\"\",\"updateTime\":\"2025-02-07 16:42:36\",\"usableColumn\":false},{\"capJavaField\":\"Password\",\"columnComment\":\"用户密码\",\"columnId\":14,\"columnName\":\"password\",\"columnType\":\"varchar(63)\",\"createBy\":\"admin\",\"createTime\":\"2024-12-22 10:25:59\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":false,\"insert\":false', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 17:17:27', 712);
INSERT INTO `sys_oper_log` VALUES (317, '代码生成', 8, 'com.fs.swap.generator.controller.GenControllerAdmin.batchGenCode()', 'GET', 1, 'admin', '研发部门', '/tool/gen/batchGenCode', '127.0.0.1', '内网IP', '{\"tables\":\"user_info\"}', NULL, 0, NULL, '2025-02-07 17:17:32', 204);
INSERT INTO `sys_oper_log` VALUES (318, '字典数据', 2, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2024-12-18 14:06:53\",\"cssClass\":\"\",\"default\":true,\"dictCode\":1,\"dictLabel\":\"未知\",\"dictSort\":1,\"dictType\":\"sys_user_sex\",\"dictValue\":\"0\",\"isDefault\":\"Y\",\"listClass\":\"info\",\"params\":{},\"remark\":\"性别男\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 17:21:27', 75);
INSERT INTO `sys_oper_log` VALUES (319, '字典数据', 2, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2024-12-18 14:06:53\",\"cssClass\":\"\",\"default\":false,\"dictCode\":2,\"dictLabel\":\"男\",\"dictSort\":1,\"dictType\":\"sys_user_sex\",\"dictValue\":\"1\",\"isDefault\":\"N\",\"listClass\":\"info\",\"params\":{},\"remark\":\"性别男\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 17:21:48', 71);
INSERT INTO `sys_oper_log` VALUES (320, '字典数据', 2, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2024-12-18 14:06:53\",\"cssClass\":\"\",\"default\":false,\"dictCode\":2,\"dictLabel\":\"男\",\"dictSort\":1,\"dictType\":\"sys_user_sex\",\"dictValue\":\"1\",\"isDefault\":\"N\",\"listClass\":\"primary\",\"params\":{},\"remark\":\"性别男\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 17:21:58', 71);
INSERT INTO `sys_oper_log` VALUES (321, '字典数据', 2, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2024-12-18 14:06:53\",\"cssClass\":\"\",\"default\":false,\"dictCode\":3,\"dictLabel\":\"女\",\"dictSort\":2,\"dictType\":\"sys_user_sex\",\"dictValue\":\"2\",\"isDefault\":\"N\",\"listClass\":\"primary\",\"params\":{},\"remark\":\"性别女\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 17:22:11', 70);
INSERT INTO `sys_oper_log` VALUES (322, '字典数据', 2, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2024-12-18 14:06:53\",\"cssClass\":\"\",\"default\":true,\"dictCode\":1,\"dictLabel\":\"未知\",\"dictSort\":1,\"dictType\":\"sys_user_sex\",\"dictValue\":\"0\",\"isDefault\":\"Y\",\"listClass\":\"info\",\"params\":{},\"remark\":\"性别未知\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 17:22:18', 69);
INSERT INTO `sys_oper_log` VALUES (323, '用户信息', 2, 'com.fs.swap.admin.controller.operation.UserInfoController.edit()', 'PUT', 1, 'admin', '研发部门', '/operation/userInfo', '127.0.0.1', '内网IP', '{\"avatar\":\"jury/system/avatar/watermelon.webp\",\"createTime\":\"2025-02-03 16:22:48\",\"deleted\":0,\"gender\":1,\"id\":1000,\"mobile\":\"17326180826\",\"nickname\":\"watermelon\",\"openid\":\"oTV_163t1KvtAmyeJ-E554x1qwlM\",\"params\":{},\"silver\":13400,\"slogan\":\"无人扶我青云志，我自踏雪至山巅\",\"status\":0,\"unionId\":\"oKWjO66-i3kuixcqiXDweHmres18\",\"updateTime\":\"2025-02-07 17:22:30\",\"userLevel\":0}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-07 17:22:34', 41);
INSERT INTO `sys_oper_log` VALUES (324, '用户信息', 5, 'com.fs.swap.admin.controller.operation.UserInfoController.export()', 'POST', 1, 'admin', '研发部门', '/operation/userInfo/export', '127.0.0.1', '内网IP', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-02-07 17:25:03', 1357);
INSERT INTO `sys_oper_log` VALUES (325, '用户信息', 5, 'com.fs.swap.admin.controller.operation.UserInfoController.export()', 'POST', 1, 'admin', '研发部门', '/operation/userInfo/export', '127.0.0.1', '内网IP', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-02-07 17:29:15', 1321);
INSERT INTO `sys_oper_log` VALUES (326, '代码生成', 6, 'com.fs.swap.generator.controller.GenControllerAdmin.importTableSave()', 'POST', 1, 'admin', '研发部门', '/tool/gen/importTable', '************', '内网IP', '{\"tables\":\"wx_id_info\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-08 15:06:00', 266);
INSERT INTO `sys_oper_log` VALUES (327, '代码生成', 2, 'com.fs.swap.generator.controller.GenControllerAdmin.editSave()', 'PUT', 1, 'admin', '研发部门', '/tool/gen', '************', '内网IP', '{\"businessName\":\"wxIdInfo\",\"className\":\"WxIdInfo\",\"columns\":[{\"capJavaField\":\"UnionId\",\"columnComment\":\"unionId\",\"columnId\":92,\"columnName\":\"union_id\",\"columnType\":\"varchar(64)\",\"createBy\":\"admin\",\"createTime\":\"2025-02-08 15:06:00\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isPk\":\"1\",\"isRequired\":\"0\",\"javaField\":\"unionId\",\"javaType\":\"String\",\"list\":false,\"params\":{},\"pk\":true,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":1,\"superColumn\":false,\"tableId\":13,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"MaOpenId\",\"columnComment\":\"小程序openId\",\"columnId\":93,\"columnName\":\"ma_open_id\",\"columnType\":\"varchar(64)\",\"createBy\":\"admin\",\"createTime\":\"2025-02-08 15:06:00\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"0\",\"javaField\":\"maOpenId\",\"javaType\":\"String\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":false,\"sort\":2,\"superColumn\":false,\"tableId\":13,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"MpOpenId\",\"columnComment\":\"公众号openId\",\"columnId\":94,\"columnName\":\"mp_open_id\",\"columnType\":\"varchar(64)\",\"createBy\":\"admin\",\"createTime\":\"2025-02-08 15:06:00\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"0\",\"javaField\":\"mpOpenId\",\"javaType\":\"String\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":false,\"sort\":3,\"superColumn\":false,\"tableId\":13,\"updateBy\":\"\",\"usableColumn\":false}],\"crud\":true,\"functionAuthor\":\"zy\",\"functionName\":\"微信id关系\",\"genPath\":\"/\",\"genType\":\"0\",\"moduleName\":\"system\",\"options\":\"{}\",\"packageName\":\"com.fs.swap\",\"params\":{},\"sub\":false,\"tableComment\":\"微信id关系表\",\"tableId\":13,\"tableName\":\"wx_id_info\",\"tplCategory\":\"crud\",\"tplWebType\":\"element-ui\",\"tree\":false}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-08 15:07:48', 165);
INSERT INTO `sys_oper_log` VALUES (328, '代码生成', 8, 'com.fs.swap.generator.controller.GenControllerAdmin.batchGenCode()', 'GET', 1, 'admin', '研发部门', '/tool/gen/batchGenCode', '************', '内网IP', '{\"tables\":\"wx_id_info\"}', NULL, 0, NULL, '2025-02-08 15:07:54', 193);
INSERT INTO `sys_oper_log` VALUES (329, '代码生成', 2, 'com.fs.swap.generator.controller.GenControllerAdmin.editSave()', 'PUT', 1, 'admin', '研发部门', '/tool/gen', '************', '内网IP', '{\"businessName\":\"wxIdInfo\",\"className\":\"WxIdInfo\",\"columns\":[{\"capJavaField\":\"UnionId\",\"columnComment\":\"unionId\",\"columnId\":92,\"columnName\":\"union_id\",\"columnType\":\"varchar(64)\",\"createBy\":\"admin\",\"createTime\":\"2025-02-08 15:06:00\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isPk\":\"1\",\"isRequired\":\"0\",\"javaField\":\"unionId\",\"javaType\":\"String\",\"list\":false,\"params\":{},\"pk\":true,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":1,\"superColumn\":false,\"tableId\":13,\"updateBy\":\"\",\"updateTime\":\"2025-02-08 15:07:48\",\"usableColumn\":false},{\"capJavaField\":\"MaOpenId\",\"columnComment\":\"小程序openId\",\"columnId\":93,\"columnName\":\"ma_open_id\",\"columnType\":\"varchar(64)\",\"createBy\":\"admin\",\"createTime\":\"2025-02-08 15:06:00\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"0\",\"javaField\":\"maOpenId\",\"javaType\":\"String\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":false,\"sort\":2,\"superColumn\":false,\"tableId\":13,\"updateBy\":\"\",\"updateTime\":\"2025-02-08 15:07:48\",\"usableColumn\":false},{\"capJavaField\":\"MpOpenId\",\"columnComment\":\"公众号openId\",\"columnId\":94,\"columnName\":\"mp_open_id\",\"columnType\":\"varchar(64)\",\"createBy\":\"admin\",\"createTime\":\"2025-02-08 15:06:00\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"0\",\"javaField\":\"mpOpenId\",\"javaType\":\"String\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":false,\"sort\":3,\"superColumn\":false,\"tableId\":13,\"updateBy\":\"\",\"updateTime\":\"2025-02-08 15:07:48\",\"usableColumn\":false}],\"crud\":true,\"functionAuthor\":\"zy\",\"functionName\":\"微信id关系\",\"genPath\":\"/\",\"genType\":\"0\",\"moduleName\":\"system\",\"options\":\"{\\\"parentMenuId\\\":0}\",\"packageName\":\"com.fs.swap.system\",\"params\":{\"parentMenuId\"', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-08 15:09:29', 140);
INSERT INTO `sys_oper_log` VALUES (330, '代码生成', 8, 'com.fs.swap.generator.controller.GenControllerAdmin.batchGenCode()', 'GET', 1, 'admin', '研发部门', '/tool/gen/batchGenCode', '************', '内网IP', '{\"tables\":\"wx_id_info\"}', NULL, 0, NULL, '2025-02-08 15:09:32', 67);
INSERT INTO `sys_oper_log` VALUES (331, '菜单管理', 2, 'com.fs.swap.admin.controller.system.SysMenuControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"operation/userInfo/index\",\"createTime\":\"2025-02-07 16:48:11\",\"icon\":\"user\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2001,\"menuName\":\"用户信息\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2000,\"path\":\"userInfo\",\"perms\":\"operation:userInfo:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-10 19:35:24', 50);
INSERT INTO `sys_oper_log` VALUES (332, '代码生成', 2, 'com.fs.swap.generator.controller.GenControllerAdmin.synchDb()', 'GET', 1, 'admin', '研发部门', '/tool/gen/synchDb/cases', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-10 19:35:58', 618);
INSERT INTO `sys_oper_log` VALUES (333, '代码生成', 2, 'com.fs.swap.generator.controller.GenControllerAdmin.editSave()', 'PUT', 1, 'admin', '研发部门', '/tool/gen', '127.0.0.1', '内网IP', '{\"businessName\":\"cases\",\"className\":\"Cases\",\"columns\":[{\"capJavaField\":\"Id\",\"columnComment\":\"id\",\"columnId\":27,\"columnName\":\"id\",\"columnType\":\"int\",\"createBy\":\"admin\",\"createTime\":\"2024-12-30 14:16:22\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":true,\"insert\":false,\"isIncrement\":\"1\",\"isInsert\":\"0\",\"isList\":\"1\",\"isPk\":\"1\",\"isQuery\":\"1\",\"isRequired\":\"0\",\"javaField\":\"id\",\"javaType\":\"Long\",\"list\":true,\"params\":{},\"pk\":true,\"query\":true,\"queryType\":\"EQ\",\"required\":false,\"sort\":1,\"superColumn\":false,\"tableId\":3,\"updateBy\":\"\",\"updateTime\":\"2025-02-10 19:35:57\",\"usableColumn\":false},{\"capJavaField\":\"UserId\",\"columnComment\":\"user_id\",\"columnId\":35,\"columnName\":\"user_id\",\"columnType\":\"int\",\"createBy\":\"admin\",\"createTime\":\"2024-12-30 14:16:22\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":false,\"insert\":false,\"isEdit\":\"0\",\"isIncrement\":\"0\",\"isInsert\":\"0\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"0\",\"javaField\":\"userId\",\"javaType\":\"Long\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":false,\"sort\":2,\"superColumn\":false,\"tableId\":3,\"updateBy\":\"\",\"updateTime\":\"2025-02-10 19:35:57\",\"usableColumn\":false},{\"capJavaField\":\"Title\",\"columnComment\":\"标题\",\"columnId\":28,\"columnName\":\"title\",\"columnType\":\"varchar(255)\",\"createBy\":\"admin\",\"createTime\":\"2024-12-30 14:16:22\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":false,\"insert\":false,\"isEdit\":\"0\",\"isIncrement\":\"0\",\"isInsert\":\"0\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"0\",\"javaField\":\"title\",\"javaType\":\"String\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"LIKE\",\"required\":false,\"sort\":3,\"superColumn\":false,\"tableId\":3,\"updateBy\":\"\",\"updateTime\":\"2025-02-10 19:35:57\",\"usableColumn\":false},{\"capJavaField\":\"Description\",\"columnComment\":\"描述\",\"columnId\":29,\"columnName\":\"description\",\"columnType\":\"text\",\"createBy\":\"admin\",\"createTime\":\"2024-12-30 14:16:22\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"textarea\",\"increment\":false,\"insert\":false,\"isE', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-10 19:42:23', 791);
INSERT INTO `sys_oper_log` VALUES (334, '代码生成', 8, 'com.fs.swap.generator.controller.GenControllerAdmin.batchGenCode()', 'GET', 1, 'admin', '研发部门', '/tool/gen/batchGenCode', '127.0.0.1', '内网IP', '{\"tables\":\"cases\"}', NULL, 0, NULL, '2025-02-10 19:42:27', 351);
INSERT INTO `sys_oper_log` VALUES (335, '代码生成', 2, 'com.fs.swap.generator.controller.GenControllerAdmin.editSave()', 'PUT', 1, 'admin', '研发部门', '/tool/gen', '127.0.0.1', '内网IP', '{\"businessName\":\"cases\",\"className\":\"Cases\",\"columns\":[{\"capJavaField\":\"Id\",\"columnComment\":\"id\",\"columnId\":27,\"columnName\":\"id\",\"columnType\":\"int\",\"createBy\":\"admin\",\"createTime\":\"2024-12-30 14:16:22\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":true,\"insert\":false,\"isIncrement\":\"1\",\"isInsert\":\"0\",\"isList\":\"1\",\"isPk\":\"1\",\"isQuery\":\"1\",\"isRequired\":\"0\",\"javaField\":\"id\",\"javaType\":\"Long\",\"list\":true,\"params\":{},\"pk\":true,\"query\":true,\"queryType\":\"EQ\",\"required\":false,\"sort\":1,\"superColumn\":false,\"tableId\":3,\"updateBy\":\"\",\"updateTime\":\"2025-02-10 19:42:23\",\"usableColumn\":false},{\"capJavaField\":\"UserId\",\"columnComment\":\"user_id\",\"columnId\":35,\"columnName\":\"user_id\",\"columnType\":\"int\",\"createBy\":\"admin\",\"createTime\":\"2024-12-30 14:16:22\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":false,\"insert\":false,\"isEdit\":\"0\",\"isIncrement\":\"0\",\"isInsert\":\"0\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"0\",\"javaField\":\"userId\",\"javaType\":\"Long\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":false,\"sort\":2,\"superColumn\":false,\"tableId\":3,\"updateBy\":\"\",\"updateTime\":\"2025-02-10 19:42:23\",\"usableColumn\":false},{\"capJavaField\":\"Title\",\"columnComment\":\"标题\",\"columnId\":28,\"columnName\":\"title\",\"columnType\":\"varchar(255)\",\"createBy\":\"admin\",\"createTime\":\"2024-12-30 14:16:22\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":false,\"insert\":false,\"isEdit\":\"0\",\"isIncrement\":\"0\",\"isInsert\":\"0\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"0\",\"javaField\":\"title\",\"javaType\":\"String\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"LIKE\",\"required\":false,\"sort\":3,\"superColumn\":false,\"tableId\":3,\"updateBy\":\"\",\"updateTime\":\"2025-02-10 19:42:23\",\"usableColumn\":false},{\"capJavaField\":\"Description\",\"columnComment\":\"描述\",\"columnId\":29,\"columnName\":\"description\",\"columnType\":\"text\",\"createBy\":\"admin\",\"createTime\":\"2024-12-30 14:16:22\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"textarea\",\"increment\":false,\"insert\":false,\"isE', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-10 19:45:47', 755);
INSERT INTO `sys_oper_log` VALUES (336, '代码生成', 8, 'com.fs.swap.generator.controller.GenControllerAdmin.batchGenCode()', 'GET', 1, 'admin', '研发部门', '/tool/gen/batchGenCode', '127.0.0.1', '内网IP', '{\"tables\":\"cases\"}', NULL, 0, NULL, '2025-02-10 19:45:51', 121);
INSERT INTO `sys_oper_log` VALUES (337, '案件列表', 5, 'com.fs.swap.admin.controller.operation.CasesController.export()', 'POST', 1, 'admin', '研发部门', '/operation/cases/export', '127.0.0.1', '内网IP', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-02-10 20:12:02', 663);
INSERT INTO `sys_oper_log` VALUES (338, '菜单管理', 2, 'com.fs.swap.admin.controller.system.SysMenuControllerAdmin.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"operation/cases/index\",\"createTime\":\"2025-02-10 20:05:43\",\"icon\":\"list\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2007,\"menuName\":\"案件列表\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2000,\"path\":\"cases\",\"perms\":\"operation:cases:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-10 20:13:59', 61);
INSERT INTO `sys_oper_log` VALUES (339, '案件列表', 5, 'com.fs.swap.admin.controller.operation.CasesController.export()', 'POST', 1, 'admin', '研发部门', '/operation/cases/export', '127.0.0.1', '内网IP', '{\"pageSize\":\"10\",\"pageNum\":\"15\"}', NULL, 0, NULL, '2025-02-10 20:17:23', 97);
INSERT INTO `sys_oper_log` VALUES (340, '字典类型', 1, 'com.fs.swap.admin.controller.system.SysDictTypeControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/type', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"dictName\":\"星座列表\",\"dictType\":\"constellation_type\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-11 14:12:27', 66);
INSERT INTO `sys_oper_log` VALUES (341, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"cssClass\":\"\",\"default\":false,\"dictLabel\":\"白羊座\",\"dictSort\":0,\"dictType\":\"constellation_type\",\"dictValue\":\"1\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-11 14:13:55', 60);
INSERT INTO `sys_oper_log` VALUES (342, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"金牛座\",\"dictSort\":0,\"dictType\":\"constellation_type\",\"dictValue\":\"2\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-11 14:14:04', 54);
INSERT INTO `sys_oper_log` VALUES (343, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"双子座\",\"dictSort\":0,\"dictType\":\"constellation_type\",\"dictValue\":\"3\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-11 14:14:13', 48);
INSERT INTO `sys_oper_log` VALUES (344, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"巨蟹座\",\"dictSort\":0,\"dictType\":\"constellation_type\",\"dictValue\":\"4\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-11 14:14:23', 50);
INSERT INTO `sys_oper_log` VALUES (345, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"狮子座\",\"dictSort\":0,\"dictType\":\"constellation_type\",\"dictValue\":\"5\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-11 14:14:33', 49);
INSERT INTO `sys_oper_log` VALUES (346, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"处女座\",\"dictSort\":0,\"dictType\":\"constellation_type\",\"dictValue\":\"6\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-11 14:14:42', 50);
INSERT INTO `sys_oper_log` VALUES (347, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"天秤座\",\"dictSort\":0,\"dictType\":\"constellation_type\",\"dictValue\":\"7\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-11 14:14:59', 51);
INSERT INTO `sys_oper_log` VALUES (348, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"天蝎座\",\"dictSort\":0,\"dictType\":\"constellation_type\",\"dictValue\":\"8\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-11 14:15:16', 52);
INSERT INTO `sys_oper_log` VALUES (349, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"射手座\",\"dictSort\":0,\"dictType\":\"constellation_type\",\"dictValue\":\"9\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-11 14:15:24', 124);
INSERT INTO `sys_oper_log` VALUES (350, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"摩羯座\",\"dictSort\":0,\"dictType\":\"constellation_type\",\"dictValue\":\"10\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-11 14:15:32', 48);
INSERT INTO `sys_oper_log` VALUES (351, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"水瓶座\",\"dictSort\":0,\"dictType\":\"constellation_type\",\"dictValue\":\"11\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-11 14:15:42', 50);
INSERT INTO `sys_oper_log` VALUES (352, '字典数据', 1, 'com.fs.swap.admin.controller.system.SysDictDataControllerAdmin.add()', 'POST', 1, 'admin', '研发部门', '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"双鱼座\",\"dictSort\":0,\"dictType\":\"constellation_type\",\"dictValue\":\"12\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-11 14:15:49', 47);
INSERT INTO `sys_oper_log` VALUES (353, '代码生成', 6, 'com.fs.swap.generator.controller.GenControllerAdmin.importTableSave()', 'POST', 1, 'admin', '研发部门', '/tool/gen/importTable', '127.0.0.1', '内网IP', '{\"tables\":\"constellation_daily_fortune\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-11 22:32:56', 522);
INSERT INTO `sys_oper_log` VALUES (354, '代码生成', 2, 'com.fs.swap.generator.controller.GenControllerAdmin.editSave()', 'PUT', 1, 'admin', '研发部门', '/tool/gen', '127.0.0.1', '内网IP', '{\"businessName\":\"constellation_daily_fortune\",\"className\":\"ConstellationDailyFortune\",\"columns\":[{\"capJavaField\":\"Id\",\"columnComment\":\"id\",\"columnId\":103,\"columnName\":\"id\",\"columnType\":\"int\",\"createBy\":\"admin\",\"createTime\":\"2025-02-11 22:32:56\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":true,\"insert\":true,\"isIncrement\":\"1\",\"isInsert\":\"1\",\"isPk\":\"1\",\"isRequired\":\"0\",\"javaField\":\"id\",\"javaType\":\"Long\",\"list\":false,\"params\":{},\"pk\":true,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":1,\"superColumn\":false,\"tableId\":14,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"Constellation\",\"columnComment\":\"星座\",\"columnId\":104,\"columnName\":\"constellation\",\"columnType\":\"varchar(10)\",\"createBy\":\"admin\",\"createTime\":\"2025-02-11 22:32:56\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"constellation\",\"javaType\":\"String\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":2,\"superColumn\":false,\"tableId\":14,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"Date\",\"columnComment\":\"日期\",\"columnId\":105,\"columnName\":\"date\",\"columnType\":\"date\",\"createBy\":\"admin\",\"createTime\":\"2025-02-11 22:32:56\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"datetime\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"date\",\"javaType\":\"Date\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":3,\"superColumn\":false,\"tableId\":14,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"Love\",\"columnComment\":\"爱情指数\",\"columnId\":106,\"columnName\":\"love\",\"columnType\":\"int\",\"createBy\":\"admin\",\"createTime\":\"2025-02-11 22:32:56\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-11 22:34:26', 461);
INSERT INTO `sys_oper_log` VALUES (355, '代码生成', 8, 'com.fs.swap.generator.controller.GenControllerAdmin.batchGenCode()', 'GET', 1, 'admin', '研发部门', '/tool/gen/batchGenCode', '127.0.0.1', '内网IP', '{\"tables\":\"constellation_daily_fortune\"}', NULL, 0, NULL, '2025-02-11 22:34:30', 363);

-- ----------------------------
-- Table structure for sys_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_post`;
CREATE TABLE `sys_post`  (
  `post_id` bigint NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
  `post_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '岗位编码',
  `post_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '岗位名称',
  `post_sort` int NOT NULL COMMENT '显示顺序',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`post_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '岗位信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_post
-- ----------------------------
INSERT INTO `sys_post` VALUES (1, 'ceo', 'ceo', 1, '0', 'admin', '2024-12-18 14:06:28', 'admin', '2025-02-07 16:29:31', '');
INSERT INTO `sys_post` VALUES (2, 'se', '项目经理', 2, '0', 'admin', '2024-12-18 14:06:28', '', NULL, '');
INSERT INTO `sys_post` VALUES (3, 'hr', '人力资源', 3, '0', 'admin', '2024-12-18 14:06:28', '', NULL, '');
INSERT INTO `sys_post` VALUES (4, 'user', '普通员工', 4, '0', 'admin', '2024-12-18 14:06:28', '', NULL, '');

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `role_id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色权限字符串',
  `role_sort` int NOT NULL COMMENT '显示顺序',
  `data_scope` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  `menu_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '菜单树选择项是否关联显示',
  `dept_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '部门树选择项是否关联显示',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 102 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES (1, '超级管理员', 'admin', 1, '1', 1, 1, '0', '0', 'admin', '2024-12-18 14:06:28', '', NULL, '超级管理员');
INSERT INTO `sys_role` VALUES (2, '普通角色', 'common', 2, '2', 0, 0, '1', '2', 'admin', '2024-12-18 14:06:28', 'admin', '2025-02-07 16:31:49', '普通角色');
INSERT INTO `sys_role` VALUES (100, '研发', 'dev', 1, '1', 1, 1, '0', '0', 'admin', '2025-02-07 16:38:45', '', NULL, NULL);
INSERT INTO `sys_role` VALUES (101, '运营', 'operation', 1, '1', 1, 1, '0', '0', 'admin', '2025-02-07 16:39:08', '', NULL, NULL);

-- ----------------------------
-- Table structure for sys_role_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_dept`;
CREATE TABLE `sys_role_dept`  (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `dept_id` bigint NOT NULL COMMENT '部门ID',
  PRIMARY KEY (`role_id`, `dept_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色和部门关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role_dept
-- ----------------------------

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`  (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `menu_id` bigint NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`role_id`, `menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色和菜单关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `user_id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `dept_id` bigint NULL DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户昵称',
  `user_type` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '00' COMMENT '用户类型（00系统用户）',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '手机号码',
  `sex` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '密码',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `login_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 101 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES (1, 103, 'admin', '若依', '00', '<EMAIL>', '***********', '0', '', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '127.0.0.1', '2025-02-16 16:51:18', 'admin', '2024-12-18 14:06:27', '', '2025-02-16 16:51:20', '管理员');
INSERT INTO `sys_user` VALUES (2, 105, 'ry', '若依', '00', '<EMAIL>', '15666666666', '1', '', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '2', '127.0.0.1', '2024-12-18 14:06:27', 'admin', '2024-12-18 14:06:27', 'admin', '2024-12-23 16:48:52', '测试员');
INSERT INTO `sys_user` VALUES (100, 100, 'zhouye', '周野', '00', '<EMAIL>', '17326180826', '0', '', '$2a$10$TmZU9YlmrvXsFLl6asg6uup8HXIoxDYCCx.c07vHD/fO20okw5H5W', '0', '0', '', NULL, 'admin', '2025-02-07 16:38:01', '', NULL, NULL);

-- ----------------------------
-- Table structure for sys_user_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_post`;
CREATE TABLE `sys_user_post`  (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `post_id` bigint NOT NULL COMMENT '岗位ID',
  PRIMARY KEY (`user_id`, `post_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户与岗位关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user_post
-- ----------------------------
INSERT INTO `sys_user_post` VALUES (1, 1);
INSERT INTO `sys_user_post` VALUES (100, 1);

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`, `role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户和角色关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user_role
-- ----------------------------
INSERT INTO `sys_user_role` VALUES (1, 1);
INSERT INTO `sys_user_role` VALUES (100, 100);

-- ----------------------------
-- Table structure for user_info
-- ----------------------------
DROP TABLE IF EXISTS `user_info`;
CREATE TABLE `user_info`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户手机号码',
  `union_id` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'union_id',
  `password` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户密码',
  `nickname` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '昵称',
  `silver` int NOT NULL DEFAULT 0 COMMENT '俸银',
  `slogan` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'slogan',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '用户头像图片',
  `gender` tinyint NULL DEFAULT 0 COMMENT '性别：0 未知， 1男， 2 女',
  `birthday` date NULL DEFAULT NULL COMMENT '生日',
  `user_level` tinyint NULL DEFAULT 0 COMMENT '用户等级',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '0 可用, 1 禁用, 2 注销',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13895 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_info
-- ----------------------------

-- ----------------------------
-- Table structure for user_invite_record
-- ----------------------------
DROP TABLE IF EXISTS `user_invite_record`;
CREATE TABLE `user_invite_record`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `inviter_user_id` int NOT NULL COMMENT '邀请人id',
  `invitee_user_id` int NOT NULL COMMENT '被邀请人id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `user_invite_record_pk_2`(`invitee_user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 339 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '邀请记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_invite_record
-- ----------------------------

-- ----------------------------
-- Table structure for user_login_record
-- ----------------------------
DROP TABLE IF EXISTS `user_login_record`;
CREATE TABLE `user_login_record`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int NOT NULL COMMENT '用户ID',
  `login_time` datetime NULL DEFAULT NULL COMMENT '登录时间',
  `ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'IP地址',
  `location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'IP归属地',
  `platform` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '客户端平台',
  `brand` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '设备品牌',
  `model` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '设备型号',
  `version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '微信版本号',
  `sdk_version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '客户端基础库版本',
  `is_first_login` tinyint(1) NULL DEFAULT 0 COMMENT '是否首次登录（0否 1是）',
  `system` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '设备系统版本',
  `device_platform` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '设备平台',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_login_record_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_user_login_record_login_time`(`login_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4127 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户登录记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_login_record
-- ----------------------------

-- ----------------------------
-- Table structure for user_silver_record
-- ----------------------------
DROP TABLE IF EXISTS `user_silver_record`;
CREATE TABLE `user_silver_record`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int NOT NULL COMMENT 'user_id',
  `event` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '事件',
  `type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '类型（1加，2减）',
  `number` int NOT NULL DEFAULT 0 COMMENT '数量',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建事件',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 771 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '俸银记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_silver_record
-- ----------------------------

-- ----------------------------
-- Table structure for user_votes
-- ----------------------------
DROP TABLE IF EXISTS `user_votes`;
CREATE TABLE `user_votes`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '投票用户ID',
  `cases_id` bigint NOT NULL COMMENT '案件ID',
  `vote_type` tinyint NOT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_case`(`user_id` ASC, `cases_id` ASC) USING BTREE COMMENT '确保用户对同一案件只能投票一次',
  INDEX `idx_case_vote_user`(`cases_id` ASC, `vote_type` ASC, `user_id` ASC, `create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 42459 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户投票' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_votes
-- ----------------------------

-- ----------------------------
-- Table structure for wx_id_info
-- ----------------------------
DROP TABLE IF EXISTS `wx_id_info`;
CREATE TABLE `wx_id_info`  (
  `union_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'unionId',
  `ma_open_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '小程序openId',
  `mp_open_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公众号openId',
  PRIMARY KEY (`union_id`) USING BTREE,
  UNIQUE INDEX `wx_id_info_pk`(`union_id` ASC, `ma_open_id` ASC, `mp_open_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信id关系表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of wx_id_info
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
