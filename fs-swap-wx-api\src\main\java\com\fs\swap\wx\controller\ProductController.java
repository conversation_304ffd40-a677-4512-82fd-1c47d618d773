package com.fs.swap.wx.controller;

import com.fs.swap.common.annotation.RepeatSubmit;
import com.fs.swap.common.core.controller.WxApiBaseController;
import com.fs.swap.common.core.domain.AjaxResult;
import com.fs.swap.common.core.domain.entity.Product;
import com.fs.swap.common.core.domain.entity.UserHome;
import com.fs.swap.common.core.domain.entity.UserInfo;
import com.fs.swap.common.core.page.TableDataInfo;
import com.fs.swap.common.enums.ProductStatus;
import com.fs.swap.common.enums.TaskEventType;
import com.fs.swap.common.enums.GreenTextType;
import com.fs.swap.common.enums.GreenImgType;
import com.fs.swap.common.exception.ServiceException;
import com.fs.swap.common.enums.ErrorType;
import com.fs.swap.common.green.ContentModerationFacade;
import com.fs.swap.common.utils.ResidentialUtils;
import com.fs.swap.system.service.*;
import com.fs.swap.wx.service.TaskEventService;
import org.dromara.x.file.storage.core.FileStorageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 商品
 */
@RestController
@RequestMapping("/product")
public class ProductController extends WxApiBaseController {
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Value("${dromara.fileDomain}")
    private String fileUrl;
    @Autowired
    private FileStorageService fileStorageService;
    @Autowired
    private ISysDictTypeService dictTypeService;
    private ISysConfigService configService;
    @Autowired
    private IProductService productService;
    @Autowired
    private IUserInfoService userInfoService;
    @Autowired
    private IUserHomeService userHomeService;

    @Autowired
    private TaskEventService taskEventService;

    @Autowired
    private ContentModerationFacade contentModerationFacade;

    @GetMapping("/product_list")
    public TableDataInfo productList(Product product) {
        // 获取小区ID：优先使用用户绑定的小区，其次使用请求参数中的小区ID
        UserHome userHome = null;
        if (isLogin()) {
            userHome = userHomeService.selectUserSelectedHome(getUserId());
        }

        Long residentialId = ResidentialUtils.getResidentialId(userHome, product.getResidentialId());
        product.setResidentialId(residentialId);

        product.setStatus(ProductStatus.ON_SALE.val());
        startPage();
        List<Product> products = productService.selectProductList(product);

        // 收集所有商品关联的用户ID
        Set<Long> userIds = products.stream()
                .map(Product::getUserId)
                .collect(Collectors.toSet());

        // 一次性查询所有用户信息
        Map<Long, UserInfo> userInfoMap = userInfoService.batchSelectUserInfoByIds(userIds);

        // 组装数据
        products.forEach(p -> {
            UserInfo userInfo = userInfoMap.get(p.getUserId());
            if (userInfo != null) {
                p.setNickname(userInfo.getNickname());
                p.setAvatar(userInfo.getAvatar());
            }
        });

        return getDataTable(products);
    }


    @GetMapping("/product_list_user")
    public TableDataInfo productListUser(Product product) {
        // 获取小区ID：优先使用用户绑定的小区，其次使用请求参数中的小区ID
        startPage();
        List<Product> products = productService.selectProductListByUserId(product);

        UserInfo userInfo = userInfoService.selectUserInfoById(product.getUserId());
        // 组装数据
        products.forEach(p -> {
            p.setNickname(userInfo.getNickname());
            p.setAvatar(userInfo.getAvatar());
        });
        return getDataTable(products);
    }

    @GetMapping("/product_info_edit/{id}")
    public AjaxResult productInfoEdit(@PathVariable Long id) {
        // 获取商品信息
        Product product = productService.selectProductWithContactById(id);

        if (product == null) {
            throw new ServiceException(ErrorType.E_5012);
        }
        // 检查是否为商品所有者
        if (!getUserId().equals(product.getUserId())) {
            throw new ServiceException(ErrorType.E_400);
        }

        return AjaxResult.success(product);
    }

    @GetMapping("/product_info/{id}")
    public AjaxResult productInfo(@PathVariable Long id) {
        // 获取商品信息
        Product product = productService.selectProductById(id);
        if (product == null) {
            throw new ServiceException(ErrorType.E_5012);
        }

        // 获取卖家信息
        UserInfo userInfo = userInfoService.selectUserInfoById(product.getUserId());
        product.setAvatar(userInfo.getAvatar());
        product.setNickname(userInfo.getNickname());

        // 更新观看人数
        Long views = product.getViews();
        if (views == null) {
            views = 0L;
        }
        product.setViews(views + 1);
        productService.updateProduct(product);

        return AjaxResult.success(product);
    }

    @PostMapping("/product_add")
    @RepeatSubmit(interval = 1000, message = "请勿重复提交，请稍后再试")
    public AjaxResult addProduct(@RequestBody Product product) {
        Long userId = getUserId();

        // 内容审核 - 商品描述
        if (product.getDescription() != null && !product.getDescription().trim().isEmpty()) {
            contentModerationFacade.checkText(product.getDescription(), GreenTextType.COMMENT_DETECTION.val());
        }

        // 内容审核 - 商品图片
        if (product.getImages() != null && !product.getImages().trim().isEmpty()) {
            String[] imageUrls = product.getImages().split(",");
            for (String imageUrl : imageUrls) {
                if (!imageUrl.trim().isEmpty()) {
                    // 构建完整的图片URL
                    String fullImageUrl = fileUrl + imageUrl.trim();
                    contentModerationFacade.checkImage(fullImageUrl, GreenImgType.POST_IMAGE_CHECK.val());
                }
            }
        }

        product.setUserId(userId);
        UserHome userHome = userHomeService.selectUserSelectedHome(userId);
        product.setResidentialId(userHome.getResidentialId());
        // 设置商品状态为在售
        product.setStatus(ProductStatus.ON_SALE.getCode());
        int result = productService.insertProduct(product);

        // 触发任务事件
        if (result > 0 && product.getId() != null) {
            try {
                // 触发发布商品任务事件
                taskEventService.processTaskEvent(userId, TaskEventType.PRODUCT_PUBLISH.val(), product.getId().toString());
                logger.info("触发商品发布任务事件，用户ID: {}, 商品ID: {}", userId, product.getId());
            } catch (Exception e) {
                // 任务事件处理失败不影响主业务
                logger.warn("任务事件处理失败，但不影响商品发布: {}", e.getMessage());
            }
        }

        return result > 0 ? AjaxResult.success(product.getId()) : AjaxResult.error();
    }

    /**
     * 更新商品信息
     */
    @PutMapping("/update/{id}")
    @RepeatSubmit(interval = 3000, message = "请勿重复提交，请稍后再试")
    public AjaxResult updateProduct(@PathVariable Long id, @RequestBody Product product) {
        // 获取当前登录用户ID
        Long userId = getUserId();

        // 获取原商品信息
        Product existingProduct = productService.selectProductById(id);
        if (existingProduct == null) {
            throw new ServiceException(ErrorType.E_5012);
        }

        // 检查是否为商品所有者
        if (!userId.equals(existingProduct.getUserId())) {
            throw new ServiceException(ErrorType.E_6004); // 复用"无权查看该订单"错误码
        }

        // 如果商品已售出，不能修改
        if (ProductStatus.SOLD.getCode().equals(existingProduct.getStatus())) {
            throw new ServiceException(ErrorType.E_5021); // 不允许修改
        }

        // 内容审核 - 商品描述（只有当描述发生变化时才审核）
        if (product.getDescription() != null && !product.getDescription().trim().isEmpty()
                && !product.getDescription().equals(existingProduct.getDescription())) {
            contentModerationFacade.checkText(product.getDescription(), GreenTextType.COMMENT_DETECTION.val());
        }

        // 内容审核 - 商品图片（只有当图片发生变化时才审核）
        if (product.getImages() != null && !product.getImages().trim().isEmpty()
                && !product.getImages().equals(existingProduct.getImages())) {
            String[] imageUrls = product.getImages().split(",");
            for (String imageUrl : imageUrls) {
                if (!imageUrl.trim().isEmpty()) {
                    // 构建完整的图片URL
                    String fullImageUrl = fileUrl + imageUrl.trim();
                    contentModerationFacade.checkImage(fullImageUrl, GreenImgType.POST_IMAGE_CHECK.val());
                }
            }
        }

        // 设置ID和用户ID
        product.setId(id);
        product.setUserId(userId);

        // 保留原有的小区ID
        product.setResidentialId(existingProduct.getResidentialId());

        // 保留原有的状态
        product.setStatus(existingProduct.getStatus());

        // 更新商品
        productService.updateProduct(product);

        return AjaxResult.success("更新成功");
    }

    /**
     * 获取当前用户发布的商品列表
     */
    @GetMapping("/my_products")
    public TableDataInfo getMyProducts(Product product) {
        // 获取当前登录用户ID
        Long userId = getUserId();

        // 设置查询条件为当前用户
        product.setUserId(userId);
        // 分页查询
        startPage();
        List<Product> products = productService.selectProductList(product);

        return getDataTable(products);
    }

    /**
     * 更新商品状态（上架/下架）
     */
    @PutMapping("/update_status/{id}")
    public AjaxResult updateProductStatus(@PathVariable Long id, @RequestBody Map<String, String> params) {
        // 获取当前登录用户ID
        Long userId = getUserId();

        // 获取商品信息
        Product product = productService.selectProductById(id);
        if (product == null) {
            throw new ServiceException(ErrorType.E_5012);
        }

        // 检查是否为商品所有者
        if (!userId.equals(product.getUserId())) {
            throw new ServiceException(ErrorType.E_6004);
        }

        // 获取要更新的状态
        String status = params.get("status");
        if (status == null) {
            throw new ServiceException(ErrorType.E_5016); // 格式错误
        }

        // 检查状态是否有效
        if (!status.equals(ProductStatus.ON_SALE.getCode()) &&
                !status.equals(ProductStatus.OFF_SHELF.getCode())) {
            throw new ServiceException(ErrorType.E_5016); // 格式错误
        }

        // 如果商品已售出，不能更改状态
        if (ProductStatus.SOLD.getCode().equals(product.getStatus())) {
            throw new ServiceException(ErrorType.E_5021); // 不允许修改
        }

        // 更新商品状态
        product.setStatus(status);
        productService.updateProduct(product);

        return AjaxResult.success("更新成功");
    }

    /**
     * 删除商品
     */
    @DeleteMapping("/delete/{id}")
    public AjaxResult deleteProduct(@PathVariable Long id) {
        // 获取当前登录用户ID
        Long userId = getUserId();

        // 获取商品信息
        Product product = productService.selectProductById(id);
        if (product == null) {
            throw new ServiceException(ErrorType.E_5012);
        }

        // 检查是否为商品所有者
        if (!userId.equals(product.getUserId())) {
            throw new ServiceException(ErrorType.E_6004);
        }

        // 只有下架状态的商品才能删除
        if (!ProductStatus.OFF_SHELF.getCode().equals(product.getStatus())) {
            throw new ServiceException(ErrorType.E_5021); // 不允许修改
        }

        // 删除商品
        productService.deleteProductById(id);

        return AjaxResult.success("删除成功");
    }
}