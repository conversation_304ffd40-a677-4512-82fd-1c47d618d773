// pages/profile/profile.js
const app = getApp()
const api = require('../../../config/api')
const config = require('../../../config/config')
const userUtils = require('../../../utils/user')
const systemInfoService = require('../../../services/systemInfo.js')

Page({
    data: {
        hasLogin: false,
        userInfo: {},
        statInfo: {},
        isLoading: false,
        showSloganPopup: false,
        newSlogan: '',
        originalSlogan: '',
        // 关注公众号提示
        showFollowWechat: false,
        // 是否正在合成输入（用于处理中文输入法）
        isComposing: false,
        // 统计信息 - 预设初始值避免页面高度跳跃
        productCount: 0,
        collectionCount: 0,
        followCount: 0,
        fansCount: 0,
        activityCount: 0,
        boughtCount: 0,
        soldCount: 0,
        totalPublished: 0,
        totalCollected: 0,
        totalOrdered: 0,
        totalSold: 0,
        // 任务相关 - 预设空数组避免高度跳跃
        tasks: [],
        taskLoading: false,
        // 个人介绍
        introduction: '',
        // 快速任务
        quickTasks: [
            //     { id: 1, name: '发布物品', desc: '发布你的闲置物品', icon: '/static/img/task-publish.png', action: '去发布' },
            //     { id: 2, name: '收藏物品', desc: '收藏感兴趣的物品', icon: '/static/img/task-collect.png', action: '去收藏' },
            {
                id: 3,
                name: '邀请好友',
                desc: '好友注册成功随机获得2-20碳豆',
                icon: '/static/img/share.png',
                action: '去邀请',
                type: 'share'
            },
            {
              id: 4,
              name: '观看视频',
              desc: '视频可随机获得2-20碳豆,每日2次',
              icon: '/static/img/share.png',
              action: '去观看',
              type:'ad'
            }
            //     { id: 4, name: '评价交易', desc: '评价你的交易体验', icon: '/static/img/task-review.png', action: '去评价' }
        ]
    },

    // 获取默认的未登录用户信息
    getDefaultUserInfo() {
        return {
            id: '',
            nickname: '',
            avatar: '',
            gender: 1,
            silver: 0,
            slogan: '',
            currentResidentialName: ''
        }
    },

    // 规范化/处理用户信息（如处理头像URL）
    async buildProcessedUserInfo(rawUserInfo) {
        const processedUserInfo = {...rawUserInfo}
        if (processedUserInfo.avatar) {
            processedUserInfo.processedAvatar = await systemInfoService.processImageUrl(processedUserInfo.avatar)
        }
        return processedUserInfo
    },

    onLoad() {
        // 初始化页面登录状态
        this.loginController = userUtils.initPageLogin(this, {
            onLoginSuccess: () => {
                // 登录成功后的处理
                this.handleLoginSuccess()
            },
            allowGuestMode: true, // 个人页面允许游客模式，显示登录提示
            guestModeCallback: () => {
                // 游客模式下的处理
                console.log('个人页面游客模式：显示登录提示')
                this.setData({hasLogin: false})
                this.showGuestMode()
            },
            initialDelay: 50 // TabBar页面使用更短的初始延迟
        })

        // 设置初始状态
        this.setData({
            hasLogin: app.globalData.hasLogin,
        })

        // 监听用户信息变化
        this._userInfoWatcher = app.watch(
            'globalData.userInfo',
            (newValue, oldValue) => {
                if (newValue) {
                    this.updateUserInfo()
                }
            }
        )

        // 初始化数据
        this.updateUserInfo()
	this.initRewardedVideoAd()
    },

    /**
     * 处理登录成功
     */
    handleLoginSuccess() {
        this.setData({
            hasLogin: true
        })
        this.updateUserInfo()
        this.updateUserStats()
        this.loadTaskData()
    },

    /**
     * 显示游客模式内容
     */
    showGuestMode() {
        this.setData({
            userInfo: {
                id: '',
                nickname: '点击登录',
                avatar: '/static/img/default_avatar.png',
                gender: 1,
                silver: 0,
                slogan: '登录后查看更多功能',
                currentResidentialName: '未登录'
            }
        })
    },

    /**
     * 处理登录按钮点击
     */
    onLoginTipClick() {
        // 显示登录框
        userUtils.showLoginComponent(this, true)
    },

    onShow() {
        // 更新登录状态
        const currentLoginStatus = app.globalData.hasLogin
        this.setData({
            hasLogin: currentLoginStatus,
        })

        // 更新用户数据
        this.updateUserInfo()
        this.updateUserStats()

        // 加载任务数据
        if (currentLoginStatus) {
            this.loadTaskData()
        }

        // 关注提示展示在 updateUserInfo 内部根据最新用户信息决定
    },

    /**
     * 显示关注公众号提示
     */
    showFollowWechatTip() {
        const hasLogin = app.safeGetGlobalData('hasLogin', false)

        // 未登录不展示
        if (!hasLogin) {
            this.setData({
                showFollowWechat: false
            })
            return
        }

        const subscribeMpIs = this.data.userInfo && this.data.userInfo.subscribeMpIs

        // subscribeMpIs 为 true -> 已关注，不展示
        if (subscribeMpIs === true) {
            this.setData({
                showFollowWechat: false
            })
            return
        }

        // subscribeMpIs 为 false -> 未关注，展示（延迟避免冲突）
        if (subscribeMpIs === false) {
            setTimeout(() => {
                this.setData({
                    showFollowWechat: true
                })
            }, 1000)
            return
        }

        // 未返回该字段时，默认不展示
        this.setData({
            showFollowWechat: false
        })
    },

    /**
     * 关闭关注公众号提示
     */
    onCloseFollowWechat() {
        this.setData({
            showFollowWechat: false
        })
    },

    /**
     * 处理关注公众号事件
     */
    onFollowWechat() {
        // 关闭弹窗
        this.setData({
            showFollowWechat: false
        })

        // 标记已经处理过关注提示
        wx.setStorageSync('hasFollowedWechat', true)

        // 跳转到微信公众号页面
        const wechatUrl = 'https://mp.weixin.qq.com/s/wUnwhdD6VOf3fyDGRpbVhg'

        // 使用 web-view 跳转到公众号
        wx.navigateTo({
            url: `/pages/webview/webview?url=${encodeURIComponent(wechatUrl)}`,
        })
    },

    onHide() {
        // 页面隐藏时不重置hasInitialized，这样返回时不会重新加载
    },

    onUnload() {
        // 清除登录控制器
        if (this.loginController) {
            this.loginController.cleanup()
            this.loginController = null
        }

        // 清理广告实例
        if (this.rewardedVideoAd) {
            this.rewardedVideoAd.destroy()
        }

        // 取消监听器
        if (this._userInfoWatcher) {
            this._userInfoWatcher()
            this._userInfoWatcher = null
        }
    },

    // 初始化激励视频广告
    initRewardedVideoAd() {
        // 检查是否支持激励视频广告
        if (!wx.createRewardedVideoAd) {
            return
        }

        this.rewardedVideoAd = wx.createRewardedVideoAd({
            adUnitId: 'adunit-292c65bff200a925'
        })

        // 监听广告加载成功
        this.rewardedVideoAd.onLoad(() => {
            // 广告加载成功
        })

        // 监听广告加载失败
        this.rewardedVideoAd.onError((err) => {
            wx.showToast({
                title: '广告加载失败',
                icon: 'none'
            })
        })

        // 监听用户关闭广告
        this.rewardedVideoAd.onClose((res) => {
            if (res && res.isEnded) {
                // 正常播放结束，发放奖励
                this.giveReward()
            } else {
                // 播放中途退出，不发放奖励
                wx.showToast({
                    title: '观看完整广告才能获得奖励',
                    icon: 'none'
                })
            }
        })
    },

    // 登录失败事件处理
    onLoginFailed(e) {
        wx.showToast({
            title: '登录失败，请重试',
            icon: 'none'
        })
    },

    // 获取用户信息
    async updateUserInfo() {
        try {
            const hasLogin = app.safeGetGlobalData('hasLogin', false)
            if (!hasLogin) {
                this.setData({ hasLogin: false, userInfo: this.getDefaultUserInfo() })
                return
            }

            let latestUserInfo = null
            // 优先尝试从服务器拉取
            try {
                const res = await api.loginUserInfo()
                if (res?.code === 200 && res?.data) {
                    latestUserInfo = res.data
                    // 覆盖全局数据
                    app.globalData.userInfo = latestUserInfo
                }
            } catch (apiError) {
                console.error('从服务器获取用户信息失败，使用本地缓存：', apiError)
            }

            // 服务器失败则回退到全局缓存
            if (!latestUserInfo) {
                latestUserInfo = app.safeGetGlobalData('userInfo') || null
            }

            if (latestUserInfo) {
                const processed = await this.buildProcessedUserInfo(latestUserInfo)
                this.setData({
                    userInfo: processed,
                    hasLogin: true,
                })
                // 根据订阅状态展示/隐藏关注提示
                this.showFollowWechatTip()
            } else {
                // 本地也没有用户信息
                this.setData({ hasLogin: false, userInfo: this.getDefaultUserInfo() })
            }
        } catch (err) {
            console.error('获取用户信息失败：', err)
            this.setData({ hasLogin: false, userInfo: this.getDefaultUserInfo() })
        }
    },


    // 获取用户统计数据
    async updateUserStats() {
        try {
            api.getUserStats()
                .then((res) => {
                    this.setData({
                        productCount: res.data?.productCount || 0,
                        collectionCount: res.data?.collectionCount || 0,
                        soldCount: res.data?.soldCount || 0,
                        boughtCount: res.data?.boughtCount || 0,
                        followCount: res.data?.followCount || 0,
                        fansCount: res.data?.fansCount || 0,
                        activityCount: res.data?.activityCount || 0,
                    })
                })
                .catch((err) => {
                    console.error('获取用户统计数据失败：', err)
                    // 静默处理错误，保持默认值
                })
        } catch (err) {
            console.error('获取用户统计数据失败：', err)
            // 静默处理错误
        }
    },

    // 观看广告获得奖励
    showRewardedVideoAd() {
        if (this.rewardedVideoAd) {
            this.rewardedVideoAd.show().catch(err => {
                this.rewardedVideoAd.load().then(() => this.rewardedVideoAd.show())
            })
        }
    },

    // 处理下拉刷新
    onPullDownRefresh() {
        console.log('下拉刷新')

        // 如果已登录，刷新所有数据
        if (this.data.hasLogin) {
            Promise.all([
                this.updateUserInfo(),
                this.updateUserStats(),
                this.loadTaskData()
            ]).then(() => {
                console.log('下拉刷新完成')
            }).catch((error) => {
                console.error('下拉刷新失败:', error)
            }).finally(() => {
                wx.stopPullDownRefresh()
            })
        } else {
            // 未登录状态下也可以尝试刷新登录状态
            this.updateUserInfo().finally(() => {
                wx.stopPullDownRefresh()
            })
        }
    },

    /**
     * 确保登录并执行回调的辅助方法
     * @param {Function} callback 登录成功后的回调函数
     * @returns {boolean} 是否已登录
     */
    ensureLogin(callback) {
        return userUtils.ensureLogin(this, callback)
    },

    /**
     * 跳转到个人资料页面
     */
    goProfile() {
        this.ensureLogin(() => {
            wx.navigateTo({
                url: '/pages/user/profile/profile',
            })
        })
    },

    /**
     * 跳转到当前用户的用户主页
     */
    goUserHome() {
        this.ensureLogin(() => {
            const userId = this.data?.userInfo?.id
            if (!userId) {
                wx.showToast({
                    title: '用户信息未就绪',
                    icon: 'none'
                })
                return
            }
            wx.navigateTo({
                url: `/pages/user/userProfile/userProfile?userId=${userId}`,
            })
        })
    },

    /**
     * 登录失败回调
     * @param {Object} e 事件对象
     */
    onLoginFail(e) {
        // 使用统一的错误处理
        if (e?.detail?.error) {
            userUtils.handleLoginError(e.detail.error)
        } else {
            wx.showToast({
                title: '登录失败',
                icon: 'none',
            })
        }
    },

    viewDetail() {
        this.ensureLogin(() => {
            wx.navigateTo({
                url: '/pages/user/silverDetail/silverDetail',
            })
        })
    },

    handleQuickTask(e) {
        const taskId = e.currentTarget.dataset.id
        const task = this.data.quickTasks.find((t) => t.id === taskId)

        this.ensureLogin(() => {
            if (task.type === 'share') {
                wx.showShareMenu({
                    withShareTicket: true,
                    menus: ['shareAppMessage'],
                    showShareItems: ['shareAppMessage'],
                })
            } else if (task.type === 'ad') {
                if (this.rewardedVideoAd) {
                    this.showRewardedVideoAd()
                } else {
                    wx.showToast({
                        title: '暂不支持视频广告',
                        icon: 'none',
                    })
                }
            }
        })
    },

    onShareAppMessage() {
        const userInfo = wx.getStorageSync('userInfo')
        const userId = userInfo.id || ''
        const timestamp = new Date().getTime()

        return {
            title: '海量资源任你选',
            path: `/pages/index/index?inviterUserId=${userId}&ts=${timestamp}`,
            success: function (res) {
                wx.showToast({
                    title: '分享成功',
                    icon: 'success',
                })
            },
            fail: function (res) {
                wx.showToast({
                    title: '分享失败',
                    icon: 'error',
                })
            },
        }
    },

    // 处理功能卡片按钮点击
    navigateToPage(e) {
        const page = e.currentTarget.dataset.page

        this.ensureLogin(() => {
            if (page === 'bought') {
                wx.navigateTo({
                    url: '/pages/order/list?role=buyer',
                })
            } else if (page === 'sold') {
                wx.navigateTo({
                    url: '/pages/order/list?role=seller',
                })
            } else if (page === 'favorite') {
                wx.navigateTo({
                    url: '/pages/user/collection/collection',
                })
            } else if (page === 'idle') {
                wx.navigateTo({
                    url: '/pages/user/myProducts/myProducts',
                })
            } else if (page === 'follow') {
                wx.navigateTo({
                    url: '/pages/user/follow/follow',
                })
            } else if (page === 'activity') {
                wx.navigateTo({
                    url: '/pages/user/activity/index',
                })
            } else if (page === 'taskList') {
                wx.navigateTo({
                    url: '/pages/task/task',
                })
            }
        })
    },

    // 处理"更多"按钮点击
    showAllServices() {
        this.ensureLogin(() => {
            // 目前跳转到首页
            wx.switchTab({
                url: '/pages/index/index',
            })

            // 显示提示信息
            wx.showToast({
                title: '更多服务开发中',
                icon: 'none',
                duration: 1500,
            })
        })
    },

    /**
     * 显示个性签名修改弹窗
     */
    showSloganPopup() {
        this.ensureLogin(() => {
            this.setData({
                showSloganPopup: true,
                newSlogan: this.data.userInfo.slogan || '',
                originalSlogan: this.data.userInfo.slogan || ''
            })
        })
    },

    /**
     * 处理中文输入法开始输入
     */
    onCompositionStart() {
        this.setData({
            isComposing: true
        })
    },

    /**
     * 处理中文输入法结束输入
     */
    onCompositionEnd() {
        this.setData({
            isComposing: false
        })
    },

    /**
     * 处理个性签名输入变化
     * @param {Object} event 事件对象
     */
    onSloganChange(event) {
        const value = event.detail

        // 如果正在输入拼音，不做限制
        if (this.data.isComposing) {
            this.setData({
                newSlogan: value
            })
            return
        }

        // 非拼音输入时，限制字数
        this.setData({
            newSlogan: value.slice(0, 15)
        })
    },

    /**
     * 关闭个性签名弹窗
     */
    onSloganPopupClose() {
        this.setData({
            showSloganPopup: false,
            newSlogan: '',
            originalSlogan: ''
        })
    },

    /**
     * 保存修改后的个性签名
     */
    handleChangeSlogan() {
        const {newSlogan} = this.data
        const trimmedSlogan = newSlogan.trim()

        if (!trimmedSlogan) {
            wx.showToast({
                title: '签名不能为空',
                icon: 'none'
            })
            return
        }

        if (trimmedSlogan === this.data.userInfo.slogan) {
            wx.showToast({
                title: '未作任何修改',
                icon: 'none'
            })
            return
        }

        // 检查格式是否合法
        if (this.checkSensitiveContent(trimmedSlogan)) {
            wx.showToast({
                title: '签名格式不合法',
                icon: 'none'
            })
            return
        }

        wx.showLoading({
            title: '保存中...',
            mask: true
        })

        api.changeSlogan(trimmedSlogan).then(res => {
            wx.hideLoading()
            wx.showToast({
                title: '修改成功',
                icon: 'success'
            })

            this.updateUserInfo()
            // 直接更新本地数据，避免重新请求完整用户信息
            this.setData({
                showSloganPopup: false
            })
        }).catch(err => {
            wx.hideLoading()
            wx.showToast({
                title: err.message || '网络异常，请重试',
                icon: 'none'
            })
        })
    },

    /**
     * 检查内容格式是否合法
     * @param {String} content 要检查的内容
     * @returns {Boolean} 是否包含不合法格式
     */
    checkSensitiveContent(content) {
        // 只检查是否包含过多特殊字符
        const specialChars = content.match(/[^\u4e00-\u9fa5a-zA-Z0-9\s,.!?，。！？]/g) || []
        if (specialChars.length > content.length / 3) {
            return true
        }

        return false
    },

    /**
     * 加载任务数据
     */
    async loadTaskData() {
        this.setData({taskLoading: true})

        try {
            // 只请求任务列表
            const tasksRes = await api.getTaskList()

            if (tasksRes && tasksRes.code === 200) {
                const tasks = Array.isArray(tasksRes.data) ? tasksRes.data : []

                // 确保任务数据的完整性
                const processedTasks = tasks
                    .map(task => ({
                        id: task.id || '',
                        taskCode: task.taskCode || '',
                        taskName: task.taskName || '任务名称',
                        taskDesc: task.taskDesc || '任务描述',
                        taskType: task.taskType || 'DAILY',
                        taskTypeName: task.taskTypeName || '每日任务',
                        status: task.status || 0,
                        statusName: task.statusName || '进行中',
                        currentCount: task.currentCount || 0,
                        targetCount: task.targetCount || 1,
                        progressPercent: task.progressPercent || 0,
                        rewardSilver: task.rewardSilver || 0,
                        canClaim: task.canClaim || false,
                        isCompleted: task.isCompleted || false,
                        page: task.page || ''
                    }))

                this.setData({
                    tasks: processedTasks,
                    taskLoading: false
                })
            } else {
                throw new Error('数据格式错误')
            }
        } catch (error) {
            console.error('加载任务数据失败:', error)
            wx.showToast({
                title: '加载失败',
                icon: 'error'
            })
            this.setData({
                taskLoading: false,
                tasks: []
            })
        }
    },

    /**
     * 领取单个任务奖励
     */
    async claimReward(e) {
        const taskId = e.currentTarget.dataset.taskId
        if (!taskId) return

        try {
            wx.showLoading({title: '领取中...'})

            const res = await api.claimTaskReward(taskId)

            if (res.code === 200) {
                wx.showToast({
                    title: '领取成功',
                    icon: 'success'
                })

                // 刷新任务数据和用户信息
                this.loadTaskData()
                this.updateUserInfo()
            } else {
                throw new Error(res.msg || '领取失败')
            }
        } catch (error) {
            console.error('领取奖励失败:', error)
            wx.showToast({
                title: error.message || '领取失败',
                icon: 'error'
            })
        } finally {
            wx.hideLoading()
        }
    },

    /**
     * 跳转到任务相关页面
     */
    goToTask(e) {
        const taskCode = e.currentTarget.dataset.taskCode
        const task = this.data.tasks.find(t => t.taskCode === taskCode)

        if (task && task.page) {
            // 确保路径以 / 开头
            const pagePath = task.page.startsWith('/') ? task.page : `/${task.page}`

            // 判断是否是 tabbar 页面
            const tabbarPages = ['/pages/index/index', '/pages/message/index', '/pages/publish/index', '/pages/community/index', '/pages/user/index']

            if (tabbarPages.includes(pagePath)) {
                wx.switchTab({
                    url: pagePath,
                    fail: (err) => {
                        console.error('页面跳转失败:', err)
                        wx.showToast({
                            title: '页面跳转失败',
                            icon: 'none'
                        })
                    }
                })
            } else {
                wx.navigateTo({
                    url: pagePath,
                    fail: (err) => {
                        console.error('页面跳转失败:', err)
                        wx.showToast({
                            title: '页面跳转失败',
                            icon: 'none'
                        })
                    }
                })
            }
        } else {
            wx.showToast({
                title: '功能开发中',
                icon: 'none'
            })
        }
    },

    // 处理登录成功事件（登录组件调用）
    onLoginSuccess() {
        if (this.loginController) {
            this.loginController.handleLoginSuccess()
        }
    },

    /**
     * 联系客服
     */
    contactCustomerService() {
        // 检查基础库版本
        if (!wx.canIUse('openCustomerServiceChat')) {
            wx.showToast({
                title: '当前微信版本过低，无法使用客服功能',
                icon: 'none',
                duration: 2000
            })
            return
        }

        // 检查客服配置
        const customerServiceConfig = config.CUSTOMER_SERVICE
        if (!customerServiceConfig.SERVICE_URL || !customerServiceConfig.CORP_ID) {
            wx.showToast({
                title: '客服功能暂未配置，请联系管理员',
                icon: 'none',
                duration: 2000
            })
            return
        }

        // 打开微信客服
        wx.openCustomerServiceChat({
            extInfo: {
                url: customerServiceConfig.SERVICE_URL
            },
            corpId: customerServiceConfig.CORP_ID,
            showMessageCard: false,
            sendMessageTitle: customerServiceConfig.MESSAGE_CARD.TITLE,
            sendMessagePath: customerServiceConfig.MESSAGE_CARD.PATH,
            sendMessageImg: customerServiceConfig.MESSAGE_CARD.IMAGE,
            success: (res) => {
                console.log('客服聊天打开成功', res)
            },
            fail: (err) => {
                console.error('客服聊天打开失败', err)
                wx.showToast({
                    title: '客服功能暂时无法使用，请稍后重试',
                    icon: 'none',
                    duration: 2000
                })
            }
        })
    },

    /**
     * 意见反馈
     */
    userFeedback() {
        wx.navigateTo({
            url: '/pages/feedback/index'
        })
    },
})
