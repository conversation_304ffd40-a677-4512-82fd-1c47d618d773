/* pages/community-help-publish/index.wxss */
.container {
  background: linear-gradient(180deg, #f7f8fa 0%, #ffffff 100%);
  min-height: 100vh;
  padding: 16px;
  box-sizing: border-box;
}

.content-card, .trade-card {
  background: #fff;
  border-radius: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  margin-bottom: 20px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.content-card:active, .trade-card:active {
  transform: scale(0.98);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.04);
}

/* 标题输入区域样式 */
.title-area {
  padding: 24px 24px 16px 24px;
  position: relative;
}

.title-input {
  width: 100%;
  border: none;
  outline: none;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  background: transparent;
  line-height: 1.5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.title-input::placeholder {
  color: #999;
  font-size: 16px;
  font-weight: normal;
}

.title-count {
  position: absolute;
  bottom: 4px;
  right: 24px;
  font-size: 12px;
  color: #999;
  background: rgba(255, 255, 255, 0.8);
  padding: 2px 6px;
  border-radius: 8px;
}

.description-area {
  padding: 24px;
  position: relative;
}

.description-area textarea {
  width: 100%;
  min-height: 120px;
  border: none;
  outline: none;
  font-size: 15px;
  line-height: 1.6;
  color: #333;
  background: transparent;
  resize: none;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.description-area textarea::placeholder {
  color: #999;
  font-size: 15px;
}

.word-count {
  position: absolute;
  bottom: 12px;
  right: 24px;
  font-size: 12px;
  color: #999;
  background: rgba(255, 255, 255, 0.8);
  padding: 2px 6px;
  border-radius: 8px;
}

.upload-section {
  padding: 16px 24px 24px;
}

/* 修改上传组件样式 */
.van-uploader {
  width: 100%;
}

.van-uploader__wrapper {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8rpx;
}

.van-uploader__upload,
.van-uploader__preview {
  width: calc(33.33% - 16rpx) !important;
  margin: 8rpx !important;
  box-sizing: border-box;
}

.van-uploader__upload,
.van-uploader__preview-image {
  height: 240rpx !important;
  border-radius: 12rpx;
  overflow: hidden;
}

/* 确保图片不被拉伸 */
.van-uploader__preview-image {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  background-color: #f5f5f5;
}

/* 设置预览容器固定高度 */
.van-uploader__preview {
  height: 240rpx !important;
  overflow: hidden;
}

/* 上传按钮样式优化 */
.van-uploader__upload {
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  border: 2rpx dashed #d9d9d9;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 24rpx;
}

.van-uploader__upload:active {
  background: linear-gradient(135deg, #f0f0f0, #f8f9fa);
  border-color: #3B7FFF;
  color: #3B7FFF;
}

/* 条件选择样式 - 与发布闲置保持一致 */
.condition-section {
  padding: 12px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.condition-label {
  font-size: 15px;
  color: #333;
  font-weight: 500;
  letter-spacing: 0.2px;
}

.condition-value {
  color: #666;
  font-size: 15px;
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.condition-value:active {
  opacity: 0.8;
}

.divider {
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.06), transparent);
  margin: 0 24px;
}

.contact-icons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.arrow {
  margin-left: 8px;
  color: #ccc;
  font-size: 14px;
}

/* 费用提示样式 - 无背景 */
.fee-notice {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px 20px;
  margin: 16px 0;
}

.fee-text {
  font-size: 14px;
  color: #ff6b6b;
  margin-left: 8px;
  font-weight: 500;
}

.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.04);
  border-top: 1px solid rgba(255, 255, 255, 0.8);
}

.publish-btn {
  width: 100%;
  background: linear-gradient(135deg, #34d399, #10b981);
  color: #fff;
  border: none;
  border-radius: 24px;
  font-size: 15px;
  font-weight: 500;
  padding: 10px 0;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.25);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  letter-spacing: 0.3px;
  position: relative;
  overflow: hidden;
}

.publish-btn:active {
  transform: translateY(1px);
  box-shadow: 0 2px 6px rgba(16, 185, 129, 0.2);
}

.publish-btn[disabled] {
  background: linear-gradient(135deg, #e0e0e0, #d0d0d0);
  color: #999;
  box-shadow: none;
  transform: none;
}

/* 联系方式弹窗样式 - 与发布闲置保持一致 */
.contact-picker-popup {
  background: #f7f8fa;
  border-radius: 20px 20px 0 0;
  padding: 0;
  min-height: 400px;
  max-height: 70vh;
  overflow: hidden;
}

.contact-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 16px 24px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
}

.contact-picker-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-icon {
  padding: 8px;
  color: #999;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-icon:active {
  background: #f5f5f5;
}

.contact-picker-content {
  padding: 16px;
  background: #f7f8fa;
}

.contact-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 16px;
  background: #fff;
  border-radius: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.2s ease;
}

.contact-item:last-child {
  margin-bottom: 0;
}

.contact-item:active {
  transform: scale(0.98);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.contact-item-divider {
  height: 0;
  margin: 0;
}

.contact-item-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.contact-item-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
  min-width: 0;
}

.contact-item-label {
  font-size: 15px;
  color: #333;
  font-weight: 500;
}

.contact-item-value {
  font-size: 13px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.contact-item-value-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.qr-preview-mini {
  display: flex;
  align-items: center;
  gap: 8px;
}

.qr-mini-image {
  width: 28px;
  height: 28px;
  border-radius: 6px;
  border: 1px solid #e5e5e5;
  background: #f9f9f9;
}

.contact-item-right {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.contact-item-status {
  font-size: 12px;
  color: #999;
  padding: 2px 8px;
  background: #f5f5f5;
  border-radius: 8px;
}

/* 二维码上传样式 - 与发布闲置保持一致 */
.qr-upload-container {
  padding: 20px;
  text-align: center;
}

.qr-preview-wrapper {
  width: 200px;
  height: 200px;
  margin: 0 auto 16px;
  border: 2px dashed #ddd;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  transition: all 0.3s ease;
}

.qr-preview-wrapper:active {
  border-color: #4080FF;
  background: #f0f8ff;
}

.qr-preview {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

.qr-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;
}

.qr-placeholder-text {
  margin-top: 8px;
  font-size: 14px;
}

.contact-tips {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
}

.contact-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 12px 16px 0 16px;
  padding: 8px 12px;
  background: rgba(255, 107, 107, 0.08);
  border-radius: 12px;
  border: 1px solid rgba(255, 107, 107, 0.15);
}

.contact-tip text {
  font-size: 12px;
  color: #ff6b6b;
  margin-left: 4px;
}

/* 发布确认弹窗样式 */
.publish-dialog-content {
  padding: 30rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333333;
  line-height: 1.6;
}

/* 话题选择器样式 */
.van-popup {
  border-radius: 24rpx 24rpx 0 0;
}

.van-picker__toolbar {
  border-bottom: 1rpx solid #f0f0f0;
}
