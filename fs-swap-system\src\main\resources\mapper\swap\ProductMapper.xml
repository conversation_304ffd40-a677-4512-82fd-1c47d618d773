<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fs.swap.system.mapper.ProductMapper">

    <resultMap type="Product" id="ProductResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="buyerId"    column="buyer_id"    />
        <result property="residentialId"    column="residential_id"    />
        <result property="title"    column="title"    />
        <result property="description"    column="description"    />
        <result property="price"    column="price"    />
        <result property="status"    column="status"    />
        <result property="images"    column="images"    />
        <result property="views"    column="views"    />
        <result property="collections"    column="collections"    />
        <result property="wear"    column="wear"    />
        <result property="contactInfo"    column="contact_info"    />
        <result property="deleted"    column="deleted"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectProductVo">
        select id, user_id, buyer_id, residential_id, title, description, price, status, images, views, collections, wear, deleted, create_time, update_time from product
    </sql>

    <sql id="selectProductWithContactVo">
        select id, user_id, buyer_id, residential_id, title, description, price, status, images, views, collections, wear, contact_info, deleted, create_time, update_time from product
    </sql>

    <select id="selectProductList" parameterType="Product" resultMap="ProductResult">
        <include refid="selectProductVo"/>
        <where>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="buyerId != null ">and buyer_id = #{buyerId}</if>
            <if test="residentialId != null ">and residential_id = #{residentialId}</if>
            <if test="title != null  and title != ''">and title = #{title}</if>
            <if test="description != null  and description != ''">and description like concat('%', #{description},
                '%')
            </if>
            <if test="price != null ">and price = #{price}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="images != null  and images != ''">and images = #{images}</if>
            <if test="views != null ">and views = #{views}</if>
            <if test="collections != null ">and collections = #{collections}</if>
            <if test="wear != null  and wear != ''">and wear = #{wear}</if>
            <if test="createTime != null ">and create_time = #{createTime}</if>
            <if test="updateTime != null ">and update_time = #{updateTime}</if>
            and deleted = 0
        </where>
    </select>


    <select id="selectProductListByUserId" parameterType="Product" resultMap="ProductResult">
        <include refid="selectProductVo"/>
        <where>
            <if test="userId != null ">and user_id = #{userId}</if>
            and deleted = 0
        </where>
        order by status asc,create_time desc
    </select>


    <select id="selectProductById" parameterType="Long" resultMap="ProductResult">
        <include refid="selectProductVo"/>
        where id = #{id}
    </select>

    <select id="selectProductWithContactById" parameterType="Long" resultMap="ProductResult">
        <include refid="selectProductWithContactVo"/>
        where id = #{id}
    </select>

    <insert id="insertProduct" parameterType="Product" useGeneratedKeys="true" keyProperty="id">
        insert into product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="buyerId != null">buyer_id,</if>
            <if test="residentialId != null">residential_id,</if>
            <if test="title != null">title,</if>
            <if test="description != null">description,</if>
            <if test="price != null">price,</if>
            <if test="status != null">status,</if>
            <if test="images != null">images,</if>
            <if test="views != null">views,</if>
            <if test="collections != null">collections,</if>
            <if test="wear != null">wear,</if>
            <if test="contactInfo != null">contact_info,</if>
            <if test="deleted != null">deleted,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="buyerId != null">#{buyerId},</if>
            <if test="residentialId != null">#{residentialId},</if>
            <if test="title != null">#{title},</if>
            <if test="description != null">#{description},</if>
            <if test="price != null">#{price},</if>
            <if test="status != null">#{status},</if>
            <if test="images != null">#{images},</if>
            <if test="views != null">#{views},</if>
            <if test="collections != null">#{collections},</if>
            <if test="wear != null">#{wear},</if>
            <if test="contactInfo != null">#{contactInfo},</if>
            <if test="deleted != null">#{deleted},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateProduct" parameterType="Product">
        update product
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="buyerId != null">buyer_id = #{buyerId},</if>
            <if test="residentialId != null">residential_id = #{residentialId},</if>
            <if test="title != null">title = #{title},</if>
            <if test="description != null">description = #{description},</if>
            <if test="price != null">price = #{price},</if>
            <if test="status != null">status = #{status},</if>
            <if test="images != null">images = #{images},</if>
            <if test="views != null">views = #{views},</if>
            <if test="collections != null">collections = #{collections},</if>
            <if test="wear != null">wear = #{wear},</if>
            <if test="contactInfo != null">contact_info = #{contactInfo},</if>
            <if test="deleted != null">deleted = #{deleted},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteProductById" parameterType="Long">
        update product set deleted = 1 where id = #{id}
    </update>

    <update id="deleteProductByIds" parameterType="String">
        update product set deleted = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>