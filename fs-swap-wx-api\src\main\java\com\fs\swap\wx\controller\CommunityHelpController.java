package com.fs.swap.wx.controller;

import com.fs.swap.common.annotation.RepeatSubmit;
import com.fs.swap.common.core.controller.WxApiBaseController;
import com.fs.swap.common.core.domain.AjaxResult;
import com.fs.swap.common.core.domain.entity.*;
import com.fs.swap.common.core.page.TableDataInfo;
import com.fs.swap.common.enums.*;
import com.fs.swap.common.exception.ServiceException;
import com.fs.swap.common.green.ContentModerationFacade;
import com.fs.swap.common.utils.ResidentialUtils;
import com.fs.swap.system.service.*;
import com.google.common.base.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.HashMap;

import java.util.stream.Collectors;

/**
 * 邻里互助Controller
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@RestController
@RequestMapping("/community-help")
public class CommunityHelpController extends WxApiBaseController {
    @Autowired
    private ICommunityHelpService communityHelpService;

    @Autowired
    private IUserInfoService userInfoService;

    @Autowired
    private IUserHomeService userHomeService;

    @Autowired
    private ContentModerationFacade contentModerationFacade;

    @Autowired
    private IResidentialAreaService residentialAreaService;

    @Value("${dromara.fileDomain}")
    private String fileUrl;

    /**
     * 获取邻里互助列表
     */
    @GetMapping("/list")
    public TableDataInfo list(CommunityHelp communityHelp) {
        // 获取小区ID：优先使用用户绑定的小区，其次使用请求参数中的小区ID
        UserHome userHome = null;
        if (isLogin()) {
            userHome = userHomeService.selectUserSelectedHome(getUserId());
        }

        Long residentialId = ResidentialUtils.getResidentialId(userHome, communityHelp.getResidentialId());
        communityHelp.setResidentialId(residentialId);

        // 只查询正常状态的互助信息
        communityHelp.setStatus(ProductStatus.ON_SALE.val()); // 1-正常

        startPage();
        List<CommunityHelp> communityHelps = communityHelpService.selectCommunityHelpList(communityHelp);

        // 收集所有互助信息关联的用户ID
        Set<Long> userIds = communityHelps.stream()
                .map(CommunityHelp::getUserId)
                .collect(Collectors.toSet());

        // 一次性查询所有用户信息
        Map<Long, UserInfo> userInfoMap = userInfoService.batchSelectUserInfoByIds(userIds);

        // 组装数据
        communityHelps.forEach(help -> {
            UserInfo userInfo = userInfoMap.get(help.getUserId());
            if (userInfo != null) {
                help.setNickname(userInfo.getNickname());
                help.setAvatar(userInfo.getAvatar());
            }

            // 处理第一张图片（逗号分隔格式）
            if (help.getImages() != null && !help.getImages().isEmpty()) {
                String images = help.getImages();
                String[] imageArray = images.split(",");
                if (imageArray.length > 0 && !imageArray[0].trim().isEmpty()) {
                    help.setFirstImage(imageArray[0].trim());
                }
            }

            // 清除联系方式信息，列表接口不返回联系方式
            help.setContactInfo(null);
        });

        return getDataTable(communityHelps);
    }

    /**
     * 获取邻里互助详情
     */
    @GetMapping("/detail/{id}")
    public AjaxResult detail(@PathVariable Long id) {
        // 获取互助信息
        CommunityHelp communityHelp = communityHelpService.selectCommunityHelpById(id);
        if (communityHelp == null) {
            throw new ServiceException(ErrorType.E_5012);
        }
        // 获取发布者信息
        UserInfo userInfo = userInfoService.selectUserInfoById(communityHelp.getUserId());
        if (userInfo != null) {
            // 设置发布者头像，如果头像不为空且不是完整URL，则添加文件服务器前缀
            String avatar = userInfo.getAvatar();
            if (!Strings.isNullOrEmpty(avatar) && !avatar.startsWith("http")) {
                avatar = fileUrl + avatar;
            }
            communityHelp.setAvatar(avatar);
            communityHelp.setNickname(userInfo.getNickname());
        }

        // 增加浏览次数
        communityHelpService.incrementViewCount(id);

        // 清除联系方式信息，详情接口不返回联系方式
        communityHelp.setContactInfo(null);

        return AjaxResult.success(communityHelp);
    }

    /**
     * 获取邻里互助详情
     */
    @GetMapping("/help_detail_edit/{id}")
    public AjaxResult detailEdit(@PathVariable Long id) {
        // 获取互助信息
        CommunityHelp communityHelp = communityHelpService.selectCommunityHelpById(id);
        if (communityHelp == null) {
            throw new ServiceException(ErrorType.E_5012);
        }
        // 检查是否为商品所有者
        if (!getUserId().equals(communityHelp.getUserId())) {
            throw new ServiceException(ErrorType.E_400);
        }
        return AjaxResult.success(communityHelp);
    }

    /**
     * 发布邻里互助
     */
    @PostMapping("/publish")
    @RepeatSubmit
    @Transactional
    public AjaxResult publish(@RequestBody CommunityHelp communityHelp) {
        // 获取当前登录用户ID
        Long userId = getUserId();

        // 获取用户信息，检查积分是否足够
        UserInfo userInfo = userInfoService.selectUserInfoById(userId);
        if (userInfo == null) {
            throw new ServiceException(ErrorType.E_6003);
        }

        // 检查发布费用
        Long publishFee = 10L; // 固定10碳豆
        if (userInfo.getSilver() < publishFee) {
            throw new ServiceException(ErrorType.E_6004);
        }

        // 获取用户小区信息
        UserHome userHome = userHomeService.selectUserSelectedHome(userId);
        if (userHome != null) {
            communityHelp.setResidentialId(userHome.getResidentialId());
            // 通过小区ID获取社区ID
            ResidentialArea residential = residentialAreaService.selectResidentialAreaById(userHome.getResidentialId());
            if (residential != null) {
                communityHelp.setCommunityId(residential.getCommunityId());
            }
        }

        // 设置基本信息
        communityHelp.setUserId(userId);
        communityHelp.setViewCount(0);
        communityHelp.setStatus(ProductStatus.ON_SALE.val());
        communityHelp.setDeleted(0);

        // 内容审核
        if (communityHelp.getContent() != null && !communityHelp.getContent().trim().isEmpty()) {
            contentModerationFacade.checkText(communityHelp.getContent(), GreenTextType.COMMENT_DETECTION.val());
        }
        // 内容审核 - 商品图片
        if (communityHelp.getImages() != null && !communityHelp.getImages().trim().isEmpty()) {
            String[] imageUrls = communityHelp.getImages().split(",");
            for (String imageUrl : imageUrls) {
                if (!imageUrl.trim().isEmpty()) {
                    // 构建完整的图片URL
                    String fullImageUrl = fileUrl + imageUrl.trim();
                    contentModerationFacade.checkImage(fullImageUrl, GreenImgType.POST_IMAGE_CHECK.val());
                }
            }
        }


        // 保存互助信息
        int result = communityHelpService.insertCommunityHelp(communityHelp);
        if (result <= 0) {
            throw new ServiceException(ErrorType.E_500);
        }

        // 扣除发布费用
        userInfoService.deductSilverAndRecord(userId, publishFee, SilverEventType.COMMUNITY_HELP_PUBLISH);

        return AjaxResult.success("发布成功");
    }

    /**
     * 更新邻里互助
     */
    @PutMapping("/update")
    @RepeatSubmit
    @Transactional
    public AjaxResult update(@RequestBody CommunityHelp communityHelp) {
        // 获取当前登录用户ID
        Long userId = getUserId();

        // 检查互助信息是否存在
        CommunityHelp existingHelp = communityHelpService.selectCommunityHelpById(communityHelp.getId());
        if (existingHelp == null) {
            throw new ServiceException(ErrorType.E_5012);
        }

        // 检查是否是发布者本人
        if (!existingHelp.getUserId().equals(userId)) {
            throw new ServiceException(ErrorType.E_403);
        }

        // 检查状态
        if (!ProductStatus.ON_SALE.val().equals(existingHelp.getStatus())) { // 1-正常
            throw new ServiceException(ErrorType.E_5012);
        }

        // 内容审核 - 标题和内容
        if (communityHelp.getTitle() != null && !communityHelp.getTitle().trim().isEmpty()
                && !communityHelp.getTitle().equals(existingHelp.getTitle())) {
            contentModerationFacade.checkText(communityHelp.getTitle(), GreenTextType.COMMENT_DETECTION.val());
        }

        if (communityHelp.getContent() != null && !communityHelp.getContent().trim().isEmpty()
                && !communityHelp.getContent().equals(existingHelp.getContent())) {
            contentModerationFacade.checkText(communityHelp.getContent(), GreenTextType.COMMENT_DETECTION.val());
        }

        // 内容审核 - 图片（只有当图片发生变化时才审核）
        if (communityHelp.getImages() != null && !communityHelp.getImages().trim().isEmpty()
                && !communityHelp.getImages().equals(existingHelp.getImages())) {
            String[] imageUrls = communityHelp.getImages().split(",");
            for (String imageUrl : imageUrls) {
                if (!imageUrl.trim().isEmpty()) {
                    // 构建完整的图片URL
                    String fullImageUrl = fileUrl + imageUrl.trim();
                    contentModerationFacade.checkImage(fullImageUrl, GreenImgType.POST_IMAGE_CHECK.val());
                }
            }
        }

        // 只更新允许修改的字段
        existingHelp.setTitle(communityHelp.getTitle());
        existingHelp.setContent(communityHelp.getContent());
        existingHelp.setCategory(communityHelp.getCategory());
        existingHelp.setPublishType(communityHelp.getPublishType());
        existingHelp.setEndTime(communityHelp.getEndTime());
        existingHelp.setImages(communityHelp.getImages());
        existingHelp.setContactInfo(communityHelp.getContactInfo());

        // 更新互助信息
        int result = communityHelpService.updateCommunityHelp(existingHelp);
        if (result <= 0) {
            throw new ServiceException(ErrorType.E_500);
        }

        return AjaxResult.success("更新成功");
    }


    /**
     * 获取我发布的互助
     */
    @GetMapping("/my-published")
    public TableDataInfo myPublished(CommunityHelp communityHelp) {
        // 设置当前用户ID
        communityHelp.setUserId(getUserId());

        startPage();
        List<CommunityHelp> communityHelps = communityHelpService.selectCommunityHelpList(communityHelp);

        // 处理数据
        communityHelps.forEach(help -> {
            // 处理第一张图片（逗号分隔格式）
            if (help.getImages() != null && !help.getImages().isEmpty()) {
                String images = help.getImages();
                String[] imageArray = images.split(",");
                if (imageArray.length > 0 && !imageArray[0].trim().isEmpty()) {
                    help.setFirstImage(imageArray[0].trim());
                }
            }
        });

        return getDataTable(communityHelps);
    }

    /**
     * 更新邻里互助状态（上架/下架）
     */
    @PutMapping("/update-status/{id}")
    @RepeatSubmit
    @Transactional
    public AjaxResult updateStatus(@PathVariable Long id, @RequestBody Map<String, String> params) {
        // 获取当前登录用户ID
        Long userId = getUserId();

        // 获取互助信息
        CommunityHelp communityHelp = communityHelpService.selectCommunityHelpById(id);
        if (communityHelp == null) {
            throw new ServiceException(ErrorType.E_5012);
        }

        // 检查是否是发布者本人
        if (!communityHelp.getUserId().equals(userId)) {
            throw new ServiceException(ErrorType.E_403);
        }

        // 获取要更新的状态
        String status = params.get("status");
        if (status == null) {
            throw new ServiceException(ErrorType.E_403); // 格式错误
        }

        // 检查状态是否有效
        if (!status.equals(ProductStatus.ON_SALE.getCode()) &&
                !status.equals(ProductStatus.OFF_SHELF.getCode())) {
            throw new ServiceException(ErrorType.E_403); // 格式错误
        }

        // 更新状态
        communityHelp.setStatus(status);
        int result = communityHelpService.updateCommunityHelp(communityHelp);
        if (result <= 0) {
            throw new ServiceException(ErrorType.E_500);
        }

        String message = ProductStatus.ON_SALE.getCode().equals(status) ? "上架成功" : "下架成功";
        return AjaxResult.success(message);
    }

    /**
     * 删除邻里互助
     */
    @DeleteMapping("/delete/{id}")
    @RepeatSubmit
    @Transactional
    public AjaxResult delete(@PathVariable Long id) {
        // 获取当前登录用户ID
        Long userId = getUserId();

        // 获取互助信息
        CommunityHelp communityHelp = communityHelpService.selectCommunityHelpById(id);
        if (communityHelp == null) {
            throw new ServiceException(ErrorType.E_5012);
        }

        // 检查是否是发布者本人
        if (!communityHelp.getUserId().equals(userId)) {
            throw new ServiceException(ErrorType.E_403);
        }

        // 只能删除已下架的互助
        if (!ProductStatus.OFF_SHELF.getCode().equals(communityHelp.getStatus())) {
            throw new ServiceException("只能删除已下架的互助");
        }

        // 删除互助信息
        int result = communityHelpService.deleteCommunityHelpByIds(new Long[]{id});
        if (result <= 0) {
            throw new ServiceException(ErrorType.E_500);
        }

        return AjaxResult.success("删除成功");
    }

    /**
     * 获取小区需求和服务数量统计
     */
    @GetMapping("/stats")
    public AjaxResult getStats(@RequestParam(required = false) Long residentialId) {
        // 获取小区ID：优先使用用户绑定的小区，其次使用请求参数中的小区ID
        UserHome userHome = null;
        if (isLogin()) {
            userHome = userHomeService.selectUserSelectedHome(getUserId());
        }

        Long finalResidentialId = ResidentialUtils.getResidentialId(userHome, residentialId);

        // 构建查询条件
        CommunityHelp queryCondition = new CommunityHelp();
        queryCondition.setResidentialId(finalResidentialId);
        queryCondition.setStatus(ProductStatus.ON_SALE.val()); // 只查询正常状态的

        // 获取所有数据
        List<CommunityHelp> allHelps = communityHelpService.selectCommunityHelpList(queryCondition);

        // 按类型统计
        long requireCount = allHelps.stream()
                .filter(help -> "1".equals(help.getPublishType()))
                .count();

        long serviceCount = allHelps.stream()
                .filter(help -> "2".equals(help.getPublishType()))
                .count();

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("requireCount", requireCount);
        result.put("serviceCount", serviceCount);

        return AjaxResult.success(result);
    }
}