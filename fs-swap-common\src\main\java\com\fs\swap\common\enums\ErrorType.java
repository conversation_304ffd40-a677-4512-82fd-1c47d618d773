package com.fs.swap.common.enums;

/**
 * 用户状态
 */
public enum ErrorType {
    E_400(400, "请求参数错误"),
    E_401(401, "请登录后访问"),
    E_403(403, "无权操作"),
    E_404(404, "数据不存在"),
    E_500(500, "系统异常,请稍后再试"),
    E_5001(5001, "获取手机信息异常,请稍后再试"),
    E_5002(5002, "暂不支持用户名密码登录"),
    E_5003(5003, "重复点击"),
    E_5004(5004, "上传失败"),
    E_5005(5005, "枚举类型转换失败"),
    E_5006(5006, "内容审查失败"),
    E_5007(5007, "内容违规"),
    E_5008(5008, "请选择小区"),
    E_5009(5009, "碳豆余额不足"),
    E_5010(5010, "已绑定小区"),
    E_5011(5011, "完整功能需登录"),
    E_5012(5012, "数据不存在"),
    E_5013(5013, "请先选择您的小区"),
    E_5014(5014, "发布次数不足"),
    E_5016(5016, "格式错误"),
    E_5017(5017, "每日最多2次"),
    E_5018(5018, "案件已经结束"),
    E_5019(5019, "地址解析异常"),
    E_5020(5020, "不能关注自己"),
    E_5021(5021, "无法修改"),


    E_4001(4001, "不是你的小区"),

    // 订单相关错误
    E_6001(6001, "商品已售出或已下架"),
    E_6002(6002, "不能购买自己发布的商品"),
    E_6003(6003, "用户信息不存在"),
    E_6004(6004, "无权查看该订单"),
    E_6005(6005, "只有买家可以确认收货"),
    E_6006(6006, "订单状态不正确，无法确认收货"),
    E_6007(6007, "只有买家可以取消订单"),
    E_6008(6008, "当前状态不可取消"),
    E_6009(6009, "只有买家可以申请退款"),
    E_6010(6010, "订单状态不正确，无法申请退款"),
    E_6011(6011, "只有卖家可以同意退款"),
    E_6012(6012, "订单状态不正确，无法同意退款"),
    E_6013(6013, "只有卖家可以拒绝退款"),
    E_6014(6014, "订单状态不正确，无法拒绝退款"),
    E_6015(6015, "创建订单失败"),
    E_6016(6016, "无效的角色参数"),
    E_6017(6017, "确认收货失败"),
    E_6018(6018, "取消订单失败"),
    E_6019(6019, "申请退款失败"),
    E_6020(6020, "同意退款失败"),
    E_6021(6021, "拒绝退款失败"),
    E_6022(6021, "无法修改订单状态"),
    ;

    private final int code;
    private final String msg;

    ErrorType(int code, String info) {
        this.code = code;
        this.msg = info;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
