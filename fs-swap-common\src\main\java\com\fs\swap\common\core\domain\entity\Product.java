package com.fs.swap.common.core.domain.entity;

import com.fs.swap.common.annotation.Excel;
import com.fs.swap.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 商品列表对象 product
 *
 * <AUTHOR>
 * @date 2025-03-22
 */
@Data
public class Product extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 卖家id */
    @Excel(name = "卖家id")
    private Long userId;

    /** 买家id */
    @Excel(name = "买家id")
    private Long buyerId;

    /** 所属小区ID */
    @Excel(name = "所属小区ID")
    private Long residentialId;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 描述 */
    @Excel(name = "描述")
    private String description;

    /** 积分价格 */
    @Excel(name = "积分价格")
    private Double price;

    /** 1-在售, 2-已售, 3-下架 */
    @Excel(name = "1-在售, 2-已售, 3-下架")
    private String status;

    /** 图片 */
    @Excel(name = "图片")
    private String images;

    /** 观看人数 */
    @Excel(name = "观看人数")
    private Long views;

    /** 收藏人数 */
    @Excel(name = "收藏人数")
    private Long collections;

    /** 成色 */
    @Excel(name = "成色")
    private String wear;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String contactInfo;

    /** 是否删除 */
    @Excel(name = "是否删除")
    private Integer deleted;

    /** 卖家昵称 */
    private String nickname;
    /** 卖家头像 */
    private String avatar;

}
