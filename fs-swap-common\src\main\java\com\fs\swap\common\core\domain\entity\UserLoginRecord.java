package com.fs.swap.common.core.domain.entity;

import com.fs.swap.common.annotation.Excel;
import com.fs.swap.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 用户登录记录对象 user_login_record
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class UserLoginRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 记录ID */
    private Long id;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 登录时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "登录时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date loginTime;

    /** IP地址 */
    @Excel(name = "IP地址")
    private String ip;

    /** IP归属地 */
    @Excel(name = "IP归属地")
    private String location;

    /** 客户端平台 */
    @Excel(name = "客户端平台")
    private String platform;

    /** 设备品牌 */
    @Excel(name = "设备品牌")
    private String brand;

    /** 设备型号 */
    @Excel(name = "设备型号")
    private String model;

    /** 微信版本号 */
    @Excel(name = "微信版本号")
    private String version;

    /** 客户端基础库版本 */
    @Excel(name = "客户端基础库版本")
    private String sdkVersion;

    /** 是否首次登录（0否 1是） */
    @Excel(name = "是否首次登录", readConverterExp = "0=否,1=是")
    private Integer isFirstLogin;

    /** 设备系统版本 */
    @Excel(name = "设备系统版本")
    private String system;

}
