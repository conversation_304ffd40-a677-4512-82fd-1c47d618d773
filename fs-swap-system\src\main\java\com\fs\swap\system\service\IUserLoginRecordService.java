package com.fs.swap.system.service;

import com.fs.swap.common.core.domain.entity.UserLoginRecord;

import java.util.List;

/**
 * 用户登录记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface IUserLoginRecordService 
{
    /**
     * 查询用户登录记录
     * 
     * @param id 用户登录记录主键
     * @return 用户登录记录
     */
    public UserLoginRecord selectUserLoginRecordById(Long id);

    /**
     * 查询用户登录记录列表
     * 
     * @param userLoginRecord 用户登录记录
     * @return 用户登录记录集合
     */
    public List<UserLoginRecord> selectUserLoginRecordList(UserLoginRecord userLoginRecord);

    /**
     * 新增用户登录记录
     * 
     * @param userLoginRecord 用户登录记录
     * @return 结果
     */
    public int insertUserLoginRecord(UserLoginRecord userLoginRecord);

    /**
     * 修改用户登录记录
     * 
     * @param userLoginRecord 用户登录记录
     * @return 结果
     */
    public int updateUserLoginRecord(UserLoginRecord userLoginRecord);

    /**
     * 批量删除用户登录记录
     * 
     * @param ids 需要删除的用户登录记录主键集合
     * @return 结果
     */
    public int deleteUserLoginRecordByIds(Long[] ids);

    /**
     * 删除用户登录记录信息
     *
     * @param id 用户登录记录主键
     * @return 结果
     */
    public int deleteUserLoginRecordById(Long id);


}
