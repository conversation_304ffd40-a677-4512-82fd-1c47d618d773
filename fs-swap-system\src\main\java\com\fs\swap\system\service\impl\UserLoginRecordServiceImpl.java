package com.fs.swap.system.service.impl;

import com.fs.swap.common.core.domain.entity.UserLoginRecord;
import com.fs.swap.common.utils.DateUtils;
import com.fs.swap.system.mapper.UserLoginRecordMapper;
import com.fs.swap.system.service.IUserLoginRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户登录记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class UserLoginRecordServiceImpl implements IUserLoginRecordService 
{
    @Autowired
    private UserLoginRecordMapper userLoginRecordMapper;

    /**
     * 查询用户登录记录
     * 
     * @param id 用户登录记录主键
     * @return 用户登录记录
     */
    @Override
    public UserLoginRecord selectUserLoginRecordById(Long id)
    {
        return userLoginRecordMapper.selectUserLoginRecordById(id);
    }

    /**
     * 查询用户登录记录列表
     * 
     * @param userLoginRecord 用户登录记录
     * @return 用户登录记录
     */
    @Override
    public List<UserLoginRecord> selectUserLoginRecordList(UserLoginRecord userLoginRecord)
    {
        return userLoginRecordMapper.selectUserLoginRecordList(userLoginRecord);
    }

    /**
     * 新增用户登录记录
     *
     * @param userLoginRecord 用户登录记录
     * @return 结果
     */
    @Override
    public int insertUserLoginRecord(UserLoginRecord userLoginRecord)
    {
        // 如果没有设置登录时间，则使用当前时间
        if (userLoginRecord.getLoginTime() == null) {
            userLoginRecord.setLoginTime(DateUtils.getNowDate());
        }
        return userLoginRecordMapper.insertUserLoginRecord(userLoginRecord);
    }

    /**
     * 修改用户登录记录
     * 
     * @param userLoginRecord 用户登录记录
     * @return 结果
     */
    @Override
    public int updateUserLoginRecord(UserLoginRecord userLoginRecord)
    {
        return userLoginRecordMapper.updateUserLoginRecord(userLoginRecord);
    }

    /**
     * 批量删除用户登录记录
     * 
     * @param ids 需要删除的用户登录记录主键
     * @return 结果
     */
    @Override
    public int deleteUserLoginRecordByIds(Long[] ids)
    {
        return userLoginRecordMapper.deleteUserLoginRecordByIds(ids);
    }

    /**
     * 删除用户登录记录信息
     *
     * @param id 用户登录记录主键
     * @return 结果
     */
    @Override
    public int deleteUserLoginRecordById(Long id)
    {
        return userLoginRecordMapper.deleteUserLoginRecordById(id);
    }


}
