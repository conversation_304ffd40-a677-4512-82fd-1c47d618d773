package com.fs.swap.wx.pojo.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class WxLoginInfoDTO implements Serializable {

    private static final long serialVersionUID = -7722430332896313642L;

    @NonNull
    private String phoneCode;
    @NonNull
    private String loginCode;
    private Long inviteUserId;
    private String inviteType;

    /**
     * 小区ID（用于自动注册时绑定用户小区）
     */
    private Long residentialId;

    /**
     * 设备信息 - 用于登录日志记录
     */
    private String platform;        // 客户端平台（miniprogram）
    private String brand;           // 设备品牌（iPhone、HUAWEI等）
    private String model;           // 设备型号（iPhone 13 Pro等）
    private String version;         // 微信版本号（8.0.32等）
    private String sdkVersion;      // 客户端基础库版本（2.29.0等）
    private String system;          // 设备系统版本（iOS 15.0、Android 11等）
}
