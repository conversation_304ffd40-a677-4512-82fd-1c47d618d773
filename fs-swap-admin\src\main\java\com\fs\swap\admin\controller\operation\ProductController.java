package com.fs.swap.admin.controller.operation;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.fs.swap.common.core.domain.entity.Product;
import com.fs.swap.system.service.IProductService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.fs.swap.common.annotation.Log;
import com.fs.swap.common.core.controller.AdminBaseController;
import com.fs.swap.common.core.domain.AjaxResult;
import com.fs.swap.common.enums.BusinessType;
import com.fs.swap.common.utils.poi.ExcelUtil;
import com.fs.swap.common.core.page.TableDataInfo;

/**
 * 商品列表Controller
 * 
 * <AUTHOR>
 * @date 2025-03-22
 */
@RestController
@RequestMapping("/operation/product")
public class ProductController extends AdminBaseController
{
    @Autowired
    private IProductService productService;

    /**
     * 查询商品列表列表
     */
    @PreAuthorize("@ss.hasPermi('operation:product:list')")
    @GetMapping("/list")
    public TableDataInfo list(Product product)
    {
        // 如果没有明确指定deleted参数，默认只查询未删除的商品
        if (product.getDeleted() == null) {
            product.setDeleted(0);
        }
        startPage();
        List<Product> list = productService.selectProductList(product);
        return getDataTable(list);
    }

    /**
     * 导出商品列表列表
     */
    @PreAuthorize("@ss.hasPermi('operation:product:export')")
    @Log(title = "商品列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Product product)
    {
        // 如果没有明确指定deleted参数，默认只导出未删除的商品
        if (product.getDeleted() == null) {
            product.setDeleted(0);
        }
        List<Product> list = productService.selectProductList(product);
        ExcelUtil<Product> util = new ExcelUtil<Product>(Product.class);
        util.exportExcel(response, list, "商品列表数据");
    }

    /**
     * 获取商品列表详细信息
     */
    @PreAuthorize("@ss.hasPermi('operation:product:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(productService.selectProductById(id));
    }

    /**
     * 新增商品列表
     */
    @PreAuthorize("@ss.hasPermi('operation:product:add')")
    @Log(title = "商品列表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Product product)
    {
        return toAjax(productService.insertProduct(product));
    }

    /**
     * 修改商品列表
     */
    @PreAuthorize("@ss.hasPermi('operation:product:edit')")
    @Log(title = "商品列表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Product product)
    {
        return toAjax(productService.updateProduct(product));
    }

    /**
     * 删除商品列表
     */
    @PreAuthorize("@ss.hasPermi('operation:product:remove')")
    @Log(title = "商品列表", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(productService.deleteProductByIds(ids));
    }
}
