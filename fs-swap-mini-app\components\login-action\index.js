// components/login_action/login_action.js
const api = require("../../config/api")
const userUtils = require("../../utils/user")

Component({
  properties: {
    // 添加属性用于外部控制
    autoClose: {
      type: Boolean,
      value: true
    }
  },

  data: {
    loginShow: false,
    checked: false,
    isLoading: false,
    lastClickTime: 0
  },

  methods: {
    /**
     * 显示登录弹窗
     */
    show() {
      this.setData({
        loginShow: true,
        checked: false // 重置协议勾选状态
      });
    },

    /**
     * 隐藏登录弹窗 - 新增方法，提供更简洁的API
     */
    hide() {
      this.setData({ loginShow: false });
    },

    /**
     * 关闭登录弹窗
     */
    onClose() {
      this.setData({ loginShow: false });
      // 触发关闭事件，通知父组件
      this.triggerEvent('close');
    },

    /**
     * 防抖处理
     * @returns {boolean} 是否可以继续操作
     */
    checkThrottle() {
      const now = Date.now();
      if (now - this.data.lastClickTime < 1000) { // 1秒内防重复点击
        return false;
      }
      this.setData({
        lastClickTime: now
      });
      return true;
    },

    /**
     * 切换协议勾选状态
     */
    onChange() {
      // 直接切换当前的选中状态
      this.setData({
        checked: !this.data.checked
      });
    },

    /**
     * 提示用户勾选协议
     */
    notify() {
      if (!this.checkThrottle()) return;
      wx.showToast({
        title: "请阅读并勾选协议",
        icon: "none"
      })
    },

    /**
     * 打开用户协议页面
     */
    openUserAgreement() {
      wx.navigateTo({
        url: '/pages/user/userAgreement/userAgreement'
      });
    },

    /**
     * 打开隐私政策页面
     */
    openPrivacyPolicy() {
      wx.navigateTo({
        url: '/pages/user/privacyPolicy/privacyPolicy'
      });
    },

    /**
     * 手机号登录入口方法
     * @param {Object} e 事件对象
     */
    async loginByPhone(e) {
      // 参数校验和防抖
      if (!e?.detail || this.data.isLoading || !this.checkThrottle()) {
        return;
      }

      try {
        await this._handleLogin(e);
        // 登录成功后触发事件通知
        this.triggerEvent('loginSuccess');

        // 延迟关闭登录弹窗，确保父组件有充分时间处理登录成功事件
        if(this.data.autoClose) {
          setTimeout(() => {
            this.setData({ loginShow: false });
          }, 100);
        }
      } catch (error) {
        // 使用统一的错误处理
        userUtils.handleLoginError(error);
        // 触发登录失败事件
        this.triggerEvent('loginFail', { error });
      }
    },

    /**
     * 处理登录逻辑
     * @param {Object} e 事件对象
     */
    async _handleLogin(e) {
      this.setData({ isLoading: true });
      wx.showLoading({ title: '正在登录', mask: true });

      try {
        const loginResult = await this._performLogin(e);
        await this._handleLoginSuccess(loginResult);
      } finally {
        wx.hideLoading();
        this.setData({ isLoading: false });
      }
    },

    /**
     * 执行登录请求
     * @param {Object} e 事件对象
     * @returns {Promise<Object>} 登录结果
     */
    async _performLogin(e) {
      // 验证手机号授权结果
      if (e.detail.errMsg !== 'getPhoneNumber:ok') {
        const error = new Error('phone_auth_denied');
        error.detail = e.detail;
        throw error;
      }

      // 获取微信登录code
      const wxLoginResult = await userUtils.wxLogin();

      // 获取设备信息
      const deviceInfo = userUtils.getDeviceInfo();

      // 构建登录参数
      const param = {
        phoneCode: e.detail.code,
        loginCode: wxLoginResult.code,
        ...deviceInfo
      }

      // 处理邀请信息
      const inviterUserId = wx.getStorageSync('inviterUserId');
      if (inviterUserId) {
        param.inviteType = '1'
        param.inviteUserId = inviterUserId
      }

      // 调用登录接口
      return await api.loginByPhone(param);
    },

    /**
     * 获取设备信息
     * 根据微信官方文档获取设备和应用信息
     * @returns {Object} 设备信息对象
     */


    /**
     * 处理登录成功
     * @param {Object} loginResult 登录结果
     */
    async _handleLoginSuccess(loginResult) {
      // 验证登录结果
      if (!loginResult || !loginResult.token) {
        throw new Error('登录失败，未获取到token');
      }

      // 保存token
      wx.setStorageSync(userUtils.CONFIG.STORAGE_KEYS.TOKEN, loginResult.token);

      // 清除待处理的邀请信息
      wx.removeStorageSync('inviterUserId');

      // 获取用户信息并更新到全局状态
      await userUtils.updateUserInfo();

      // 显示登录成功提示
      wx.showToast({
        title: '登录成功',
        icon: 'success',
        duration: 2000
      });
    }
  }
})