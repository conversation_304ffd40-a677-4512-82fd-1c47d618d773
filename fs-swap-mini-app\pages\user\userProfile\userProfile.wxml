<!-- pages/user/userProfile/userProfile.wxml -->
<scroll-view scroll-y="true" class="page-scroll" bindscrolltolower="onReachBottom" bindrefresherrefresh="onPullDownRefresh" refresher-enabled="{{true}}" refresher-triggered="{{refresherTriggered}}">
  <view class="container">
    <!-- 用户信息区域 -->
    <view class="user-info-section">
      <view class="user-header">
        <view class="user-avatar">
          <van-image round width="120rpx" height="120rpx" src="{{ userInfo.avatar || '/static/img/default_avatar.png' }}" />
        </view>
        <view class="user-info">
          <view class="user-name">{{ userInfo.nickname }}</view>
          <view class="user-residential">
            <van-icon name="wap-home-o" size="14px" color="#666" />
            <text class="residential-text">{{ userInfo.residentialName || '未绑定小区' }}</text>
          </view>
          <view class="user-signature">{{ userInfo.slogan || '这个人很懒，什么都没留下' }}</view>
        </view>
      </view>

      <view class="user-bottom">
        <view class="user-stats">
          <view class="stat-item" bindtap="viewFollowList">
            <view class="stat-value">{{ followCount }}</view>
            <view class="stat-label">关注</view>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-item" bindtap="viewFansList">
            <view class="stat-value">{{ fansCount }}</view>
            <view class="stat-label">粉丝</view>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-item">
            <view class="stat-value">{{ productCount }}</view>
            <view class="stat-label">宝贝</view>
          </view>
        </view>

        <view class="action-buttons" wx:if="{{ userId !== (app.globalData.userInfo && app.globalData.userInfo.id) }}">
          <van-button
            type="{{ isFollowed ? 'primary' : 'info' }}"
            plain="{{ isFollowed }}"
            round
            size="small"
            bindtap="toggleFollow">
            <van-icon name="{{ isFollowed ? 'star' : 'star-o' }}" size="16px" />
            {{ isFollowed ? '已关注' : '关注' }}
          </van-button>
          <button
            class="share-button"
            open-type="share">
            <van-icon name="wechat" size="16px" />
            <text>分享</text>
          </button>
        </view>
      </view>
    </view>

    <!-- Tab 切换栏 - 固定在用户信息下方 -->
    <view class="tab-bar-fixed">
      <view class="tab-item {{ activeTab === 'product' ? 'active' : '' }}" bindtap="switchTab" data-tab="product">
        <text>Ta的闲置</text>
      </view>
      <view class="tab-item {{ activeTab === 'help' ? 'active' : '' }}" bindtap="switchTab" data-tab="help">
        <text>Ta的互助</text>
      </view>
      <view class="tab-item {{ activeTab === 'activity' ? 'active' : '' }}" bindtap="switchTab" data-tab="activity">
        <text>Ta的活动</text>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="content-section">

      <!-- 商品列表区域（使用页面滚动实现自动加载更多） -->
      <view class="tab-content" wx:if="{{ activeTab === 'product' }}">
        <view class="product-list" wx:if="{{ productList.length > 0 }}">
          <view class="product-item" wx:for="{{ productList }}" wx:key="id" bindtap="viewProduct" data-id="{{ item.id }}">
            <view class="product-image-container">
              <van-image width="100%" height="200rpx" fit="cover" src="{{ item.firstImage }}" />
              <!-- 状态遮罩 -->
              <view class="product-status-overlay" wx:if="{{ item.status == '2' || item.status == 2 || item.status == '3' || item.status == 3 }}">
                <view class="status-mask"></view>
                <view class="status-text">
                  <text wx:if="{{ item.status == '2' || item.status == 2 }}">卖掉了</text>
                  <text wx:elif="{{ item.status == '3' || item.status == 3 }}">已下架</text>
                </view>
              </view>
            </view>
            <view class="product-info">
              <view class="product-name">{{ item.description }}</view>
              <view class="product-price">
                <text wx:if="{{item.price == 0}}">免费送</text>
                <text wx:else>¥{{ item.price }}</text>
              </view>
            </view>
          </view>
        </view>
        <!-- 商品列表加载状态 -->
        <view class="load-status-container" wx:if="{{ productList.length > 0 }}">
          <view class="loading-text" wx:if="{{ productLoading }}">
            <van-loading size="16px" />
            <text>加载中...</text>
          </view>
          <view class="no-more-text" wx:if="{{ !productHasMore && !productLoading }}">
            <text>没有更多了</text>
          </view>
        </view>
        <view class="empty-state" wx:else>
          <van-empty description="暂无在售商品" />
        </view>
      </view>

      <!-- 互助列表区域 -->
      <view class="tab-content" wx:if="{{ activeTab === 'help' }}">
        <view class="help-list" wx:if="{{ helpList.length > 0 }}">
          <view class="help-item" wx:for="{{ helpList }}" wx:key="id" bindtap="viewHelp" data-id="{{ item.id }}">
            <!-- 主要内容区域 -->
            <view class="main-content">
              <!-- 左侧内容 -->
              <view class="content-left">
                <!-- 标题前的标签区域 -->
                <view class="title-tags-section">
                  <view class="publish-type-tag {{ item.publishType === '1' ? 'request-tag' : 'offer-tag' }}">
                    <text class="tag-text">{{ item.publishType === '1' ? '发布需求' : '提供服务' }}</text>
                  </view>
                  <text class="tag-separator">|</text>
                  <view class="category-tag">
                    <text class="category-text">{{ item.categoryName || '其他' }}</text>
                  </view>
                </view>

                <!-- 标题 -->
                <view wx:if="{{ item.title }}" class="help-title">{{ item.title }}</view>

                <!-- 内容描述 -->
                <view class="help-content">{{ item.content || item.description }}</view>
              </view>

              <!-- 右侧图片 -->
              <view wx:if="{{ item.firstImage }}" class="content-right">
                <view class="image-container" catchtap="onPreviewImage" data-current="{{ item.imageList }}" data-index="0">
                  <van-image
                    width="100%"
                    height="100%"
                    src="{{ item.firstImage }}"
                    fit="cover"
                    radius="8"
                    lazy-load
                  />
                </view>
              </view>
            </view>

            <!-- 底部信息栏 -->
            <view class="bottom-section">
              <!-- 左侧发布时间 -->
              <view class="publish-time">{{ item.formattedCreateTime }}</view>

              <!-- 右侧统计信息 -->
              <view class="stats">
                <view class="stat-item">
                  <van-icon name="eye-o" size="14px" color="#8a8e99" />
                  <text class="stat-text">{{ item.viewCount || 0 }}</text>
                </view>
                <!-- 截止日期 -->
                <view wx:if="{{ item.endTime }}" class="end-time">
                  <van-icon name="clock-o" size="11px" color="#8a8e99" />
                  <text class="end-time-text">截止：{{ item.endTime }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- 互助列表加载状态 -->
        <view class="load-status-container" wx:if="{{ helpList.length > 0 }}">
          <view class="loading-text" wx:if="{{ helpLoading }}">
            <van-loading size="16px" />
            <text>加载中...</text>
          </view>
          <view class="no-more-text" wx:if="{{ !helpHasMore && !helpLoading }}">
            <text>没有更多了</text>
          </view>
        </view>
        <view class="empty-state" wx:else>
          <van-empty description="暂无发布的互助" />
        </view>
      </view>

      <!-- 活动列表区域（使用页面滚动实现自动加载更多） -->
      <view class="tab-content" wx:if="{{ activeTab === 'activity' }}">
        <view class="activity-list" wx:if="{{ activityList.length > 0 }}">
          <view class="activity-item" wx:for="{{ activityList }}" wx:key="id" bindtap="viewActivity" data-id="{{ item.id }}">
            <view class="activity-image-container">
              <van-image class="activity-image" width="100%" height="240rpx" fit="cover" src="{{ item.imageUrl || '/static/img/activity.png' }}" />
              <view class="activity-status-overlay">
                <van-tag custom-class="status-tag" type="{{ item.status === '1' ? 'primary' : 'warning' }}">{{ item.statusName }}</van-tag>
              </view>
            </view>
            <view class="activity-info">
              <view class="activity-title">{{ item.title }}</view>
              <view class="activity-details">
                <view class="activity-time">
                  <van-icon name="clock-o" size="14px" color="#666" />
                  <text>{{ item.formattedStartTime }}</text>
                </view>
                <view class="activity-location" wx:if="{{ item.location }}">
                  <van-icon name="location-o" size="14px" color="#666" />
                  <text>{{ item.locationName || '查看地点' }}</text>
                </view>
                <view class="activity-participants" wx:if="{{ item.participantCount !== undefined }}">
                  <van-icon name="friends-o" size="14px" color="#666" />
                  <text>{{ item.participantCount || 0 }}人参与</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- 活动列表加载状态 -->
        <view class="load-status-container" wx:if="{{ activityList.length > 0 }}">
          <view class="loading-text" wx:if="{{ activityLoading }}">
            <van-loading size="16px" />
            <text>加载中...</text>
          </view>
          <view class="no-more-text" wx:if="{{ !activityHasMore && !activityLoading }}">
            <text>没有更多了</text>
          </view>
        </view>
        <view class="empty-state" wx:else>
          <van-empty description="暂无发布活动" />
        </view>
      </view>
    </view>
  </view>
</scroll-view>

<!-- 登录组件 -->
<login-action id="loginAction" bind:loginSuccess="onLoginSuccess" bind:loginFail="onLoginFail"></login-action>
